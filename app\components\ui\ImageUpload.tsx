"use client";

import React, { useRef, useState, useCallback } from "react";
import { useImageUpload, UploadedImage } from "@/app/hooks/useImageUpload";
import Image from "next/image";

export interface ImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  uploadType?: string;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  showPreview?: boolean;
  allowDragDrop?: boolean;
}

export default function ImageUpload({
  value,
  onChange,
  uploadType = "general",
  maxWidth = 4096,
  maxHeight = 4096,
  className = "",
  placeholder = "Click to upload or drag and drop an image",
  disabled = false,
  required = false,
  showPreview = true,
  allowDragDrop = true,
}: ImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>(value || "");

  const { uploadState, uploadImage, clearError } = useImageUpload({
    uploadType,
    maxWidth,
    maxHeight,
    onUploadComplete: (image: UploadedImage) => {
      setPreviewUrl(image.secure_url);
      onChange(image.secure_url);
    },
    onUploadError: (error: string) => {
      console.error("Upload error:", error);
    },
  });

  const handleFileSelect = useCallback(
    async (file: File) => {
      if (disabled) return;

      clearError();
      await uploadImage(file);
    },
    [disabled, uploadImage, clearError]
  );

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    },
    [handleFileSelect]
  );

  const handleClick = useCallback(() => {
    if (disabled || uploadState.isUploading) return;
    fileInputRef.current?.click();
  }, [disabled, uploadState.isUploading]);

  const handleDragOver = useCallback(
    (event: React.DragEvent) => {
      if (!allowDragDrop || disabled) return;

      event.preventDefault();
      setIsDragOver(true);
    },
    [allowDragDrop, disabled]
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent) => {
      if (!allowDragDrop || disabled) return;

      event.preventDefault();
      setIsDragOver(false);
    },
    [allowDragDrop, disabled]
  );

  const handleDrop = useCallback(
    (event: React.DragEvent) => {
      if (!allowDragDrop || disabled) return;

      event.preventDefault();
      setIsDragOver(false);

      const files = Array.from(event.dataTransfer.files);
      const imageFile = files.find((file) => file.type.startsWith("image/"));

      if (imageFile) {
        handleFileSelect(imageFile);
      }
    },
    [allowDragDrop, disabled, handleFileSelect]
  );

  const handleRemoveImage = useCallback(() => {
    if (disabled) return;

    setPreviewUrl("");
    onChange("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [disabled, onChange]);

  const getUploadAreaClasses = () => {
    const baseClasses = `
      relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
      transition-all duration-200 ease-in-out
      ${className}
    `;

    if (disabled) {
      return `${baseClasses} border-gray-300 bg-gray-100 cursor-not-allowed`;
    }

    if (uploadState.isUploading) {
      return `${baseClasses} border-blue-400 bg-blue-50`;
    }

    if (isDragOver) {
      return `${baseClasses} border-blue-500 bg-blue-50`;
    }

    if (uploadState.error) {
      return `${baseClasses} border-red-400 bg-red-50 hover:border-red-500`;
    }

    return `${baseClasses} border-gray-300 hover:border-gray-400 hover:bg-gray-50`;
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={getUploadAreaClasses()}
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        role="button"
        tabIndex={0}
        aria-label="Upload image"
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
          required={required}
          title={placeholder}
          placeholder={placeholder}
        />

        {uploadState.isUploading ? (
          <div className="space-y-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-600">Uploading...</p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadState.progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500">{uploadState.progress}%</p>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <p className="text-sm text-gray-600">{placeholder}</p>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF, WebP up to 10MB
            </p>
          </div>
        )}
      </div>

      {/* Error Display */}
      {uploadState.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{uploadState.error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
                aria-label="Dismiss error"
              >
                <svg
                  className="h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Preview */}
      {showPreview && previewUrl && (
        <div className="relative">
          <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          {!disabled && (
            <button
              onClick={handleRemoveImage}
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              aria-label="Remove image"
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          )}
        </div>
      )}
    </div>
  );
}
