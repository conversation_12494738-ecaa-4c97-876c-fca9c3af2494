"use client";

import React from "react";
import { BookingTrendChartProps } from "@/lib/types/analytics-types";
import { formatChartDate, calculateBarWidth } from "@/lib/utils/analytics-utils";

export default function BookingTrendChart({ data }: BookingTrendChartProps) {
  const maxBookings = Math.max(...data.map((d) => d.bookings));

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Booking Trends (Last 7 Days)
      </h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="w-16 text-sm text-gray-600">
              {formatChartDate(item.date)}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
              <div
                className="bg-blue-600 h-4 rounded-full transition-all duration-300"
                style={{
                  width: calculateBarWidth(item.bookings, maxBookings),
                }}
                aria-label={`${item.bookings} bookings on ${formatChartDate(item.date)}`}
              ></div>
            </div>
            <div className="w-8 text-sm font-medium text-gray-900">
              {item.bookings}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
