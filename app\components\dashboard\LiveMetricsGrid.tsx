"use client";

import React from "react";
import StatCard, { StatIcons } from "./StatCard";
import { LiveMetricsGridProps, StatusColor } from "@/app/types/dashboard";

const defaultThresholds = {
  responseTime: { good: 200, warning: 500 },
  errorRate: { good: 1, warning: 5 },
  cpuUsage: { good: 60, warning: 80 },
  memoryUsage: { good: 60, warning: 80 },
};

export default function LiveMetricsGrid({
  metrics,
  loading,
  error,
  thresholds = defaultThresholds,
  className = "",
}: LiveMetricsGridProps) {
  const getStatusColor = (
    value: number,
    threshold: { good: number; warning: number }
  ): StatusColor => {
    if (value <= threshold.good) return "green";
    if (value <= threshold.warning) return "yellow";
    return "red";
  };

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}
      role="region"
      aria-label="Live system metrics"
    >
      <StatCard
        title="Response Time"
        value={`${metrics.responseTime}ms`}
        icon={<StatIcons.Performance />}
        color={getStatusColor(metrics.responseTime, thresholds.responseTime)}
        loading={loading}
        error={error}
        ariaLabel={`Response time: ${metrics.responseTime} milliseconds`}
        description="Current API response time"
        trend={
          metrics.responseTime <= thresholds.responseTime.good
            ? { value: 5, direction: "down", isGood: true }
            : metrics.responseTime >= thresholds.responseTime.warning
            ? { value: 15, direction: "up", isGood: false }
            : undefined
        }
      />

      <StatCard
        title="Active Users"
        value={metrics.activeUsers}
        icon={<StatIcons.Users />}
        color="blue"
        loading={loading}
        error={error}
        ariaLabel={`Active users: ${metrics.activeUsers}`}
        description="Users currently online"
        trend={
          metrics.activeUsers > 60
            ? { value: 8, direction: "up", isGood: true }
            : metrics.activeUsers < 40
            ? { value: 12, direction: "down", isGood: false }
            : undefined
        }
      />

      <StatCard
        title="Error Rate"
        value={`${metrics.errorRate}%`}
        icon={<StatIcons.Activity />}
        color={getStatusColor(metrics.errorRate, thresholds.errorRate)}
        loading={loading}
        error={error}
        ariaLabel={`Error rate: ${metrics.errorRate} percent`}
        description="Current error rate"
        trend={
          metrics.errorRate <= thresholds.errorRate.good
            ? { value: 2, direction: "down", isGood: true }
            : metrics.errorRate >= thresholds.errorRate.warning
            ? { value: 25, direction: "up", isGood: false }
            : undefined
        }
      />

      <StatCard
        title="Requests/Min"
        value={metrics.requestsPerMinute}
        icon={<StatIcons.Activity />}
        color="purple"
        loading={loading}
        error={error}
        ariaLabel={`Requests per minute: ${metrics.requestsPerMinute}`}
        description="Current request volume"
        trend={
          metrics.requestsPerMinute > 120
            ? { value: 10, direction: "up", isGood: true }
            : metrics.requestsPerMinute < 80
            ? { value: 15, direction: "down", isGood: false }
            : undefined
        }
      />
    </div>
  );
}

// Skeleton loader for metrics grid
export function LiveMetricsGridSkeleton({ className = "" }: { className?: string }) {
  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}
      aria-hidden="true"
    >
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-300 rounded w-24"></div>
            <div className="h-6 w-6 bg-gray-300 rounded"></div>
          </div>
          <div className="h-8 bg-gray-300 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-300 rounded w-32"></div>
        </div>
      ))}
    </div>
  );
}

// Error state for metrics grid
export function LiveMetricsGridError({
  error,
  onRetry,
  className = "",
}: {
  error: string;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}
      role="alert"
    >
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm font-medium text-red-800">Metric Error</div>
            <svg
              className="h-5 w-5 text-red-600"
              fill="currentColor"
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="text-xs text-red-600 mb-2">Failed to load</div>
          <div className="text-xs text-red-500">{error}</div>
        </div>
      ))}
      {onRetry && (
        <div className="col-span-full flex justify-center mt-4">
          <button
            type="button"
            onClick={onRetry}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
          >
            Retry Loading Metrics
          </button>
        </div>
      )}
    </div>
  );
}

// Empty state for metrics grid
export function LiveMetricsGridEmpty({ className = "" }: { className?: string }) {
  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}
    >
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm font-medium text-gray-500">No Data</div>
            <div className="h-6 w-6 bg-gray-300 rounded"></div>
          </div>
          <div className="text-2xl font-bold text-gray-400 mb-2">--</div>
          <div className="text-xs text-gray-400">Waiting for data...</div>
        </div>
      ))}
    </div>
  );
}

// Compact metrics grid for smaller spaces
export function CompactMetricsGrid({
  metrics,
  loading,
  error,
  className = "",
}: Omit<LiveMetricsGridProps, "thresholds">) {
  if (loading) {
    return <LiveMetricsGridSkeleton className={className} />;
  }

  if (error) {
    return <LiveMetricsGridError error={error} className={className} />;
  }

  return (
    <div className={`grid grid-cols-2 lg:grid-cols-4 gap-3 ${className}`}>
      <div className="bg-white rounded-lg border p-4">
        <div className="text-xs text-gray-500 mb-1">Response</div>
        <div className="text-lg font-semibold">{metrics.responseTime}ms</div>
      </div>
      <div className="bg-white rounded-lg border p-4">
        <div className="text-xs text-gray-500 mb-1">Users</div>
        <div className="text-lg font-semibold">{metrics.activeUsers}</div>
      </div>
      <div className="bg-white rounded-lg border p-4">
        <div className="text-xs text-gray-500 mb-1">Errors</div>
        <div className="text-lg font-semibold">{metrics.errorRate}%</div>
      </div>
      <div className="bg-white rounded-lg border p-4">
        <div className="text-xs text-gray-500 mb-1">Requests</div>
        <div className="text-lg font-semibold">{metrics.requestsPerMinute}/min</div>
      </div>
    </div>
  );
}
