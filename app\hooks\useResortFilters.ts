import { useState, useEffect, useCallback, useMemo } from "react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { Resort, ResortFilters } from "@/app/types/resort";
import { applyAllFilters } from "@/lib/utils/filter-utils";

interface UseResortFiltersProps {
  initialResorts: Resort[];
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
  onError?: (error: string) => void;
}

export function useResortFilters({
  initialResorts,
  enableRealTimeUpdates = false,
  updateInterval = 30000,
  onError,
}: UseResortFiltersProps) {
  const [resorts, setResorts] = useState<Resort[]>(initialResorts);
  const [filters, setFilters] = useState<ResortFilters>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  /**
   * Extract unique locations from resorts
   */
  const locations = useMemo(() => {
    const uniqueLocations = [...new Set(resorts.map(resort => resort.location))];
    return uniqueLocations.filter(Boolean).sort();
  }, [resorts]);

  /**
   * Apply filters to resorts
   */
  const filteredResorts = useMemo(() => {
    const filtered = applyAllFilters(
      resorts,
      {
        searchTerm: filters.searchTerm,
        location: filters.location,
        sortBy: filters.sortBy || "name",
        sortOrder: filters.sortOrder || "asc",
      },
      {
        searchFields: ["name", "description", "location"],
        enablePagination: false,
      }
    );

    return filtered.items;
  }, [resorts, filters]);

  /**
   * Initialize filters from URL params
   */
  useEffect(() => {
    const urlFilters: ResortFilters = {};
    
    const searchTerm = searchParams.get("search");
    const location = searchParams.get("location");
    const sortBy = searchParams.get("sortBy");
    const sortOrder = searchParams.get("sortOrder");
    
    if (searchTerm) urlFilters.searchTerm = searchTerm;
    if (location) urlFilters.location = location;
    if (sortBy) urlFilters.sortBy = sortBy as any;
    if (sortOrder) urlFilters.sortOrder = sortOrder as "asc" | "desc";
    
    setFilters(urlFilters);
  }, [searchParams]);

  /**
   * Update URL when filters change
   */
  const updateURL = useCallback((newFilters: ResortFilters) => {
    const params = new URLSearchParams();
    
    if (newFilters.searchTerm) params.set("search", newFilters.searchTerm);
    if (newFilters.location) params.set("location", newFilters.location);
    if (newFilters.sortBy) params.set("sortBy", newFilters.sortBy);
    if (newFilters.sortOrder) params.set("sortOrder", newFilters.sortOrder);
    
    const queryString = params.toString();
    const newURL = queryString ? `${pathname}?${queryString}` : pathname;
    
    router.replace(newURL, { scroll: false });
  }, [pathname, router]);

  /**
   * Handle filter changes
   */
  const updateFilters = useCallback((newFilters: Partial<ResortFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateURL(updatedFilters);
  }, [filters, updateURL]);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    setFilters({});
    updateURL({});
  }, [updateURL]);

  /**
   * Fetch updated resorts from API
   */
  const fetchUpdatedResorts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch("/api/resorts", {
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });
      
      if (response.ok) {
        const updatedResorts = await response.json();
        setResorts(updatedResorts);
      } else {
        throw new Error("Failed to fetch resorts");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update resorts";
      console.error("Error fetching updated resorts:", err);
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  }, [onError]);

  /**
   * Retry fetching resorts
   */
  const retry = useCallback(() => {
    fetchUpdatedResorts();
  }, [fetchUpdatedResorts]);

  /**
   * Real-time updates
   */
  useEffect(() => {
    if (!enableRealTimeUpdates) return;

    const interval = setInterval(fetchUpdatedResorts, updateInterval);
    return () => clearInterval(interval);
  }, [enableRealTimeUpdates, updateInterval, fetchUpdatedResorts]);

  /**
   * Get filter summary
   */
  const getFilterSummary = useCallback(() => {
    const activeFilters = Object.values(filters).filter(value => 
      value && value !== "" && value !== "all"
    ).length;

    if (activeFilters === 0) {
      return `${filteredResorts.length} resort${filteredResorts.length !== 1 ? 's' : ''} found`;
    }

    return `${filteredResorts.length} of ${resorts.length} resort${filteredResorts.length !== 1 ? 's' : ''} found (${activeFilters} filter${activeFilters === 1 ? '' : 's'} applied)`;
  }, [filters, filteredResorts.length, resorts.length]);

  /**
   * Check if filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => value && value !== "" && value !== "all");
  }, [filters]);

  /**
   * Get available sort options
   */
  const sortOptions = useMemo(() => [
    { value: "name", label: "Name" },
    { value: "location", label: "Location" },
    { value: "createdAt", label: "Date Added" },
  ], []);

  return {
    // State
    resorts: filteredResorts,
    allResorts: resorts,
    filters,
    locations,
    isLoading,
    error,

    // Actions
    updateFilters,
    clearFilters,
    retry,
    fetchUpdatedResorts,

    // Computed
    filterSummary: getFilterSummary(),
    hasActiveFilters,
    sortOptions,
    isEmpty: filteredResorts.length === 0,
    isFiltered: hasActiveFilters,

    // Stats
    totalCount: resorts.length,
    filteredCount: filteredResorts.length,
  };
}
