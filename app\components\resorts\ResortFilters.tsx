"use client";

import React from "react";
import { ResortFilters } from "@/app/types/resort";

interface ResortFiltersProps {
  filters: ResortFilters;
  onFiltersChange: (filters: Partial<ResortFilters>) => void;
  onClearFilters: () => void;
  locations: string[];
  sortOptions?: Array<{ value: string; label: string }>;
  showAdvanced?: boolean;
  className?: string;
}

const defaultSortOptions = [
  { value: "name", label: "Name" },
  { value: "location", label: "Location" },
  { value: "createdAt", label: "Date Added" },
];

export default function ResortFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  locations,
  sortOptions = defaultSortOptions,
  showAdvanced = false,
  className = "",
}: ResortFiltersProps) {
  const handleFilterChange = (key: keyof ResortFilters, value: any) => {
    onFiltersChange({ [key]: value });
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value && value !== "" && value !== "all"
  );

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Filter Resorts</h3>
        {hasActiveFilters && (
          <button
            type="button"
            onClick={onClearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
          >
            Clear all filters
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search
          </label>
          <input
            id="search"
            type="text"
            placeholder="Search resorts..."
            value={filters.searchTerm || ""}
            onChange={(e) => handleFilterChange("searchTerm", e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Location */}
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <select
            id="location"
            value={filters.location || ""}
            onChange={(e) => handleFilterChange("location", e.target.value || undefined)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Locations</option>
            {locations.map((location) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
        </div>

        {/* Sort By */}
        {showAdvanced && (
          <div>
            <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <select
              id="sortBy"
              value={filters.sortBy || "name"}
              onChange={(e) => handleFilterChange("sortBy", e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Clear Filters Button (when not advanced) */}
        {!showAdvanced && (
          <div className="flex items-end">
            <button
              type="button"
              onClick={onClearFilters}
              className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Sort Order */}
            <div>
              <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order
              </label>
              <select
                id="sortOrder"
                value={filters.sortOrder || "asc"}
                onChange={(e) => handleFilterChange("sortOrder", e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.searchTerm && (
              <FilterTag
                label={`Search: "${filters.searchTerm}"`}
                onRemove={() => handleFilterChange("searchTerm", "")}
              />
            )}
            {filters.location && (
              <FilterTag
                label={`Location: ${filters.location}`}
                onRemove={() => handleFilterChange("location", "")}
              />
            )}
            {filters.sortBy && filters.sortBy !== "name" && (
              <FilterTag
                label={`Sort: ${sortOptions.find(s => s.value === filters.sortBy)?.label}`}
                onRemove={() => handleFilterChange("sortBy", "name")}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Filter tag component
interface FilterTagProps {
  label: string;
  onRemove: () => void;
}

function FilterTag({ label, onRemove }: FilterTagProps) {
  return (
    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
      {label}
      <button
        type="button"
        onClick={onRemove}
        className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full bg-blue-200 hover:bg-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label={`Remove ${label} filter`}
      >
        <svg className="w-2 h-2" fill="currentColor" viewBox="0 0 8 8">
          <path d="M1.41 0L0 1.41 2.59 4 0 6.59 1.41 8 4 5.41 6.59 8 8 6.59 5.41 4 8 1.41 6.59 0 4 2.59 1.41 0z" />
        </svg>
      </button>
    </span>
  );
}

// Quick filter buttons
interface QuickFiltersProps {
  onFilterChange: (filters: Partial<ResortFilters>) => void;
  locations: string[];
  className?: string;
}

export function QuickFilters({ onFilterChange, locations, className = "" }: QuickFiltersProps) {
  const quickFilters = [
    { label: "All", filters: {} },
    ...locations.slice(0, 4).map(location => ({
      label: location,
      filters: { location }
    })),
  ];

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {quickFilters.map((filter) => (
        <button
          key={filter.label}
          type="button"
          onClick={() => onFilterChange(filter.filters)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          {filter.label}
        </button>
      ))}
    </div>
  );
}

// Compact filters for smaller spaces
export function CompactResortFilters({
  filters,
  onFiltersChange,
  locations,
  className = "",
}: Omit<ResortFiltersProps, "onClearFilters" | "sortOptions" | "showAdvanced">) {
  return (
    <div className={`flex flex-col sm:flex-row gap-4 ${className}`}>
      <div className="flex-1">
        <input
          type="text"
          placeholder="Search resorts..."
          value={filters.searchTerm || ""}
          onChange={(e) => onFiltersChange({ searchTerm: e.target.value })}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      <div className="sm:w-48">
        <select
          value={filters.location || ""}
          onChange={(e) => onFiltersChange({ location: e.target.value || undefined })}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Locations</option>
          {locations.map((location) => (
            <option key={location} value={location}>
              {location}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
