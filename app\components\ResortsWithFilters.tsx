"use client";

import React from "react";
import { Resort } from "@/app/types/resort";
import { useResortFilters } from "@/app/hooks/useResortFilters";
import ResortCard, { ResortGrid, ResortEmptyState } from "./resorts/ResortCard";
import ResortFilters, { QuickFilters } from "./resorts/ResortFilters";

interface ResortsWithFiltersProps {
  initialResorts: Resort[];
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
  showQuickFilters?: boolean;
  showAdvancedFilters?: boolean;
  className?: string;
  onError?: (error: string) => void;
}

export default function ResortsWithFilters({
  initialResorts,
  enableRealTimeUpdates = false,
  updateInterval = 30000,
  showQuickFilters = false,
  showAdvancedFilters = false,
  className = "",
  onError,
}: ResortsWithFiltersProps) {
  const {
    resorts: filteredResorts,
    allResorts,
    filters,
    locations,
    isLoading,
    error,
    updateFilters,
    clearFilters,
    retry,
    filterSummary,
    hasActiveFilters,
    sortOptions,
    isEmpty,
    isFiltered,
  } = useResortFilters({
    initialResorts,
    enableRealTimeUpdates,
    updateInterval,
    onError,
  });

  // Error state
  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Error Loading Resorts
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          type="button"
          onClick={retry}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Real-time update indicator */}
      {enableRealTimeUpdates && isLoading && (
        <div className="fixed top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm z-50">
          Updating...
        </div>
      )}

      {/* Quick Filters */}
      {showQuickFilters && (
        <div className="mb-6">
          <QuickFilters onFilterChange={updateFilters} locations={locations} />
        </div>
      )}

      {/* Advanced Filters */}
      <ResortFilters
        filters={filters}
        onFiltersChange={updateFilters}
        onClearFilters={clearFilters}
        locations={locations}
        sortOptions={sortOptions}
        showAdvanced={showAdvancedFilters}
        className="mb-6"
      />

      {/* Results summary */}
      <div className="flex justify-between items-center mb-6">
        <p className="text-gray-600">{filterSummary}</p>
        {enableRealTimeUpdates && (
          <div className="flex items-center text-sm text-gray-500">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            Live updates enabled
          </div>
        )}
      </div>

      {/* Resort grid */}
      {isEmpty ? (
        <ResortEmptyState
          title={
            isFiltered ? "No resorts match your filters" : "No Resorts Found"
          }
          description={
            isFiltered
              ? "Try adjusting your search criteria or clear the filters."
              : "No resorts are available at the moment."
          }
          actionLabel={isFiltered ? "Clear Filters" : undefined}
          onAction={isFiltered ? clearFilters : undefined}
        />
      ) : (
        <ResortGrid resorts={filteredResorts} loading={isLoading} />
      )}
    </div>
  );
}
