export interface AnalyticsData {
  overview: {
    totalResorts: number;
    totalRooms: number;
    activeRooms: number;
    totalBookings: number;
    totalRevenue: number;
    averageOccupancy: number;
    averageRating: number;
    totalReviews: number;
  };
  growth: {
    bookingsThisMonth: number;
    bookingsLastMonth: number;
    bookingGrowthRate: number;
  };
  topPerformingResorts: Array<{
    resort: {
      id: string;
      name: string;
      location: string;
    };
    bookings: number;
    revenue: number;
  }>;
  bookingTrends: Array<{
    date: string;
    bookings: number;
  }>;
  lastUpdated: string;
}

export interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  icon: React.ReactNode;
  color?: "blue" | "green" | "purple" | "orange" | "red";
}

export interface BookingTrendChartProps {
  data: Array<{ date: string; bookings: number }>;
}

export interface TopResortsTableProps {
  resorts: AnalyticsData["topPerformingResorts"];
}

export interface AnalyticsGridProps {
  data: AnalyticsData;
  loading?: boolean;
  error?: string | null;
}

export interface UseAnalyticsDataReturn {
  data: AnalyticsData | null;
  loading: boolean;
  error: string | null;
  refresh: () => void;
}
