import React from "react";
import { Clock, CheckCircle, XCircle } from "lucide-react";
import { Guest, GuestFilterType } from "@/lib/types/guest-types";

/**
 * Get status badge component for guest status
 */
export const getStatusBadge = (status: Guest["status"]): React.ReactElement | null => {
  switch (status) {
    case "PENDING":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </span>
      );
    case "CONFIRMED":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Confirmed
        </span>
      );
    case "CHECKED_IN":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Checked In
        </span>
      );
    case "CHECKED_OUT":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <XCircle className="w-3 h-3 mr-1" />
          Checked Out
        </span>
      );
    case "CANCELLED":
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <XCircle className="w-3 h-3 mr-1" />
          Cancelled
        </span>
      );
    default:
      return null;
  }
};

/**
 * Filter guests based on filter type
 */
export const filterGuests = (guests: Guest[], filter: GuestFilterType): Guest[] => {
  return guests.filter((guest) => {
    switch (filter) {
      case "pending":
        return guest.status === "PENDING" || guest.status === "CONFIRMED";
      case "checked_in":
        return guest.status === "CHECKED_IN";
      case "ready_checkout":
        return (
          guest.status === "CHECKED_IN" &&
          new Date(guest.checkOut || "") <= new Date()
        );
      default:
        return true;
    }
  });
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

/**
 * Get guest display name
 */
export const getGuestDisplayName = (guest: Guest): string => {
  return guest.userName || guest.userEmail;
};

/**
 * Get location display text
 */
export const getLocationDisplay = (guest: Guest): string => {
  const location = guest.resort?.name || guest.spaTreatment?.name;
  const room = guest.roomNumber ? ` - Room ${guest.roomNumber}` : "";
  return `${location}${room}`;
};

/**
 * Check if guest can be checked in
 */
export const canCheckIn = (guest: Guest): boolean => {
  return guest.status === "PENDING" || guest.status === "CONFIRMED";
};

/**
 * Check if guest can be checked out
 */
export const canCheckOut = (guest: Guest): boolean => {
  return guest.status === "CHECKED_IN";
};

/**
 * Get today's date in YYYY-MM-DD format
 */
export const getTodayDate = (): string => {
  return new Date().toISOString().split("T")[0];
};

/**
 * Validate date string
 */
export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

/**
 * Get filter display name
 */
export const getFilterDisplayName = (filter: GuestFilterType): string => {
  switch (filter) {
    case "all":
      return "All Guests";
    case "pending":
      return "Pending Check-in";
    case "checked_in":
      return "Checked In";
    case "ready_checkout":
      return "Ready for Check-out";
    default:
      return "Unknown";
  }
};
