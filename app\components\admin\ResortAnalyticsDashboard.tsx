"use client";

import React from "react";
import { useAnalyticsData } from "@/app/hooks/analytics/useAnalyticsData";
import AnalyticsGrid from "./analytics/AnalyticsGrid";
import BookingTrendChart from "./analytics/BookingTrendChart";
import TopResortsTable from "./analytics/TopResortsTable";

export default function ResortAnalyticsDashboard() {
  const { data, loading, error, refresh } = useAnalyticsData();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className="bg-white rounded-lg shadow-sm border p-6 animate-pulse"
            >
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Error Loading Analytics
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          type="button"
          onClick={refresh}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Analytics Grid */}
      <AnalyticsGrid data={data} />

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BookingTrendChart data={data.bookingTrends} />
        <TopResortsTable resorts={data.topPerformingResorts} />
      </div>
    </div>
  );
}
