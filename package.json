{"name": "kuriftu_weterpark", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup:env": "node scripts/setup-env.js --create", "validate:env": "node scripts/setup-env.js --validate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "seed": "node prisma/seed.js", "test": "jest", "test:watch": "jest --watch", "test:security": "jest __tests__/booking-security.test.ts", "test:email": "node scripts/test-email-system.js", "cleanup:tokens": "node scripts/cleanup-tokens.js", "security:audit": "npm audit && node scripts/security-check.js", "backup:db": "node scripts/backup-db.js"}, "dependencies": {"@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.4", "@next-auth/mongodb-adapter": "^1.1.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "dotenv": "^16.5.0", "framer-motion": "^12.14.0", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "multer": "^2.0.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "nodemailer": "^7.0.3", "prisma": "^6.8.2", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-toast": "^1.0.3", "recharts": "^2.15.3", "ts-node": "^10.9.2", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}