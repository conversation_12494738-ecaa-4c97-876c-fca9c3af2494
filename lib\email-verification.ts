import { prisma } from "./prisma";

import { sendEmailVerification, sendPasswordResetEmail } from "./mailer";
import { logAuditEvent } from "./security";
import crypto from "crypto";

// Token expiration times
const EMAIL_VERIFICATION_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
const PASSWORD_RESET_EXPIRY = 60 * 60 * 1000; // 1 hour

export interface VerificationTokenData {
  id: string;
  identifier: string;
  token: string;
  expires: Date;
  type: string;
  used: boolean;
  createdAt: Date;
}

/**
 * Generate a secure verification token
 */
export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString("hex");
}

/**
 * Create and send email verification token
 */
export async function createEmailVerificationToken(
  email: string,
  userName?: string
): Promise<{ success: boolean; token?: string; error?: string }> {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser?.emailVerified) {
      return {
        success: false,
        error: "Email is already verified",
      };
    }

    // Generate secure token
    const token = generateVerificationToken();
    const expires = new Date(Date.now() + EMAIL_VERIFICATION_EXPIRY);

    // Delete any existing verification tokens for this email
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
        type: "email_verification",
      },
    });

    // Create new verification token
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
        type: "email_verification",
        used: false,
      },
    });

    // Send verification email
    await sendEmailVerification(email, token, userName);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_TOKEN_CREATED",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });

    return {
      success: true,
      token,
    };
  } catch (error) {
    console.error("Error creating email verification token:", error);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_TOKEN_CREATION_FAILED",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: "Failed to create verification token",
    };
  }
}

/**
 * Verify email verification token
 */
export async function verifyEmailToken(
  email: string,
  token: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the verification token
    const verificationToken = await prisma.verificationToken.findFirst({
      where: {
        identifier: email,
        token,
        type: "email_verification",
        used: false,
      },
    });

    if (!verificationToken) {
      await logAuditEvent({
        action: "EMAIL_VERIFICATION_INVALID_TOKEN",
        resource: "email_verification",
        ip: "system",
        userAgent: "email_service",
        success: false,
        error: "Invalid or expired token",
      });

      return {
        success: false,
        error: "Invalid or expired verification token",
      };
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      await logAuditEvent({
        action: "EMAIL_VERIFICATION_EXPIRED_TOKEN",
        resource: "email_verification",
        ip: "system",
        userAgent: "email_service",
        success: false,
        error: "Token expired",
      });

      return {
        success: false,
        error: "Verification token has expired",
      };
    }

    // Mark token as used
    await prisma.verificationToken.update({
      where: { id: verificationToken.id },
      data: { used: true },
    });

    // Update user's email verification status
    await prisma.user.updateMany({
      where: { email },
      data: {
        emailVerified: new Date(),
        isActive: true,
      },
    });

    // Clean up old verification tokens for this email
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
        type: "email_verification",
        expires: { lt: new Date() },
      },
    });

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_SUCCESS",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error verifying email token:", error);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_ERROR",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: "Failed to verify email",
    };
  }
}

/**
 * Create and send password reset token
 */
export async function createPasswordResetToken(
  email: string
): Promise<{ success: boolean; token?: string; error?: string }> {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (!existingUser) {
      // Don't reveal if user exists or not for security
      return {
        success: true, // Return success to prevent email enumeration
      };
    }

    // Generate secure token
    const token = generateVerificationToken();
    const expires = new Date(Date.now() + PASSWORD_RESET_EXPIRY);

    // Delete any existing password reset tokens for this email
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
        type: "password_reset",
      },
    });

    // Create new password reset token
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
        type: "password_reset",
        used: false,
      },
    });

    // Send password reset email
    await sendPasswordResetEmail(email, token, existingUser.name || undefined);

    await logAuditEvent({
      action: "PASSWORD_RESET_TOKEN_CREATED",
      resource: "password_reset",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });

    return {
      success: true,
      token,
    };
  } catch (error) {
    console.error("Error creating password reset token:", error);

    await logAuditEvent({
      action: "PASSWORD_RESET_TOKEN_CREATION_FAILED",
      resource: "password_reset",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: "Failed to create password reset token",
    };
  }
}

/**
 * Verify password reset token
 */
export async function verifyPasswordResetToken(
  email: string,
  token: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the password reset token
    const resetToken = await prisma.verificationToken.findFirst({
      where: {
        identifier: email,
        token,
        type: "password_reset",
        used: false,
      },
    });

    if (!resetToken) {
      await logAuditEvent({
        action: "PASSWORD_RESET_INVALID_TOKEN",
        resource: "password_reset",
        ip: "system",
        userAgent: "email_service",
        success: false,
        error: "Invalid or expired token",
      });

      return {
        success: false,
        error: "Invalid or expired reset token",
      };
    }

    // Check if token has expired
    if (resetToken.expires < new Date()) {
      await logAuditEvent({
        action: "PASSWORD_RESET_EXPIRED_TOKEN",
        resource: "password_reset",
        ip: "system",
        userAgent: "email_service",
        success: false,
        error: "Token expired",
      });

      return {
        success: false,
        error: "Reset token has expired",
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error verifying password reset token:", error);

    await logAuditEvent({
      action: "PASSWORD_RESET_VERIFICATION_ERROR",
      resource: "password_reset",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return {
      success: false,
      error: "Failed to verify reset token",
    };
  }
}

/**
 * Clean up expired tokens (should be run periodically)
 */
export async function cleanupExpiredTokens(): Promise<void> {
  try {
    const result = await prisma.verificationToken.deleteMany({
      where: {
        expires: { lt: new Date() },
      },
    });

    console.log(`Cleaned up ${result.count} expired verification tokens`);

    await logAuditEvent({
      action: "EXPIRED_TOKENS_CLEANUP",
      resource: "verification_tokens",
      ip: "system",
      userAgent: "cleanup_service",
      success: true,
    });
  } catch (error) {
    console.error("Error cleaning up expired tokens:", error);

    await logAuditEvent({
      action: "EXPIRED_TOKENS_CLEANUP_FAILED",
      resource: "verification_tokens",
      ip: "system",
      userAgent: "cleanup_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
