"use client";

import React from "react";
import { User, AlertCircle } from "lucide-react";
import { GuestListProps } from "@/lib/types/guest-types";
import { LoadingSkeleton } from "@/app/components/ui/SkeletonComponents";
import GuestCard from "./GuestCard";

export default function GuestList({
  guests,
  loading,
  error,
  processingGuests,
  onCheckIn,
  onCheckOut,
  onRetry,
}: GuestListProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        <LoadingSkeleton count={5} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Error Loading Guests
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          type="button"
          onClick={onRetry}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          aria-label="Retry loading guests"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (guests.length === 0) {
    return (
      <div className="text-center py-8">
        <User className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No Guests Found
        </h3>
        <p className="text-gray-600">
          No guests found for the selected date and filter.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {guests.map((guest) => (
        <GuestCard
          key={guest.id}
          guest={guest}
          isProcessing={processingGuests.has(guest.id)}
          onCheckIn={onCheckIn}
          onCheckOut={onCheckOut}
        />
      ))}
    </div>
  );
}
