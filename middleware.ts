import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";
import {
  rateLimit,
  validateCSRF,
  getSecurityHeaders,
  logAuditEvent,
  getClientIP,
} from "@/lib/security";

export async function middleware(req: NextRequest) {
  const response = NextResponse.next();
  const pathname = new URL(req.url).pathname;

  // Add security headers to all responses
  const securityHeaders = getSecurityHeaders();
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Enhanced rate limiting for API routes
  if (pathname.startsWith("/api/")) {
    let rateLimitType: "default" | "auth" | "sensitive" = "default";

    // Determine rate limit type based on endpoint
    if (pathname.startsWith("/api/auth/")) {
      rateLimitType = "auth";
    } else if (
      pathname.includes("/reset-password") ||
      pathname.includes("/verify-email") ||
      pathname.includes("/admin/")
    ) {
      rateLimitType = "sensitive";
    }

    if (!rateLimit(req, rateLimitType)) {
      const clientIP = getClientIP(req);
      await logAuditEvent({
        action: "RATE_LIMIT_EXCEEDED",
        resource: pathname,
        ip: clientIP,
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: `Rate limit exceeded for ${rateLimitType} endpoints`,
        metadata: { rateLimitType, endpoint: pathname },
      });
      return NextResponse.json(
        {
          error: "Too many requests",
          message: "Rate limit exceeded. Please try again later.",
          retryAfter: rateLimitType === "sensitive" ? 300 : 900, // 5 or 15 minutes
        },
        { status: 429 }
      );
    }
  }

  // CSRF protection for state-changing operations
  if (
    ["POST", "PUT", "PATCH", "DELETE"].includes(req.method) &&
    pathname.startsWith("/api/")
  ) {
    if (!validateCSRF(req)) {
      const clientIP = getClientIP(req);
      await logAuditEvent({
        action: "CSRF_VALIDATION_FAILED",
        resource: pathname,
        ip: clientIP,
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "CSRF validation failed",
        metadata: { method: req.method, origin: req.headers.get("origin") },
      });
      return NextResponse.json(
        { error: "Invalid request origin" },
        { status: 403 }
      );
    }
  }

  // Role-based access control for protected routes
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  const userRole = token?.role?.toLowerCase();

  if (pathname.startsWith("/admin")) {
    if (!token || userRole !== "admin") {
      const clientIP = getClientIP(req);
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: clientIP,
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for admin area",
        metadata: { requiredRole: "admin", userRole: userRole || "none" },
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  if (pathname.startsWith("/manager")) {
    if (!token || userRole !== "manager") {
      const clientIP = getClientIP(req);
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: clientIP,
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for manager area",
        metadata: { requiredRole: "manager", userRole: userRole || "none" },
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  if (pathname.startsWith("/reception")) {
    if (!token || userRole !== "receptionist") {
      const clientIP = getClientIP(req);
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: clientIP,
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for reception area",
        metadata: {
          requiredRole: "receptionist",
          userRole: userRole || "none",
        },
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  return response;
}

export const config = {
  matcher: [
    // Protected routes
    "/admin/:path*",
    "/manager/:path*",
    "/reception/:path*",
    // API routes for security headers and rate limiting
    "/api/:path*",
    // Dashboard routes
    "/dashboard/:path*",
  ],
};
