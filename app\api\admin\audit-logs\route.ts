import { NextRequest, NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";
import { prisma } from "@/lib/prisma";
import { logAuditEvent, getClientIP } from "@/lib/security";

export async function GET(req: NextRequest) {
  try {
    // Check admin authorization
    const authResult = await requireRole(["admin"]);
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const { user } = authResult;
    const { searchParams } = new URL(req.url);
    
    // Extract query parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";
    const action = searchParams.get("action") || "";
    const success = searchParams.get("success");
    const userId = searchParams.get("userId") || "";
    const dateFrom = searchParams.get("dateFrom");
    const dateTo = searchParams.get("dateTo");
    const exportData = searchParams.get("export") === "true";

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { action: { contains: search, mode: "insensitive" } },
        { resource: { contains: search, mode: "insensitive" } },
        { ip: { contains: search, mode: "insensitive" } },
        { error: { contains: search, mode: "insensitive" } },
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
      ];
    }

    if (action) {
      where.action = { contains: action, mode: "insensitive" };
    }

    if (success !== null && success !== "") {
      where.success = success === "true";
    }

    if (userId) {
      where.userId = userId;
    }

    if (dateFrom || dateTo) {
      where.timestamp = {};
      if (dateFrom) {
        where.timestamp.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.timestamp.lte = new Date(dateTo + "T23:59:59.999Z");
      }
    }

    // Log the audit log access
    await logAuditEvent({
      userId: user.id,
      action: "AUDIT_LOGS_ACCESSED",
      resource: "/api/admin/audit-logs",
      ip: getClientIP(req),
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
      metadata: {
        filters: { action, success, userId, dateFrom, dateTo, search },
        export: exportData,
      },
    });

    if (exportData) {
      // Export all matching records as CSV
      const logs = await prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: { timestamp: "desc" },
      });

      // Generate CSV
      const csvHeaders = [
        "Timestamp",
        "Action",
        "Resource",
        "User Name",
        "User Email",
        "IP Address",
        "User Agent",
        "Success",
        "Error",
        "Metadata",
      ];

      const csvRows = logs.map((log) => [
        log.timestamp.toISOString(),
        log.action,
        log.resource,
        log.user?.name || "",
        log.user?.email || "",
        log.ip,
        log.userAgent,
        log.success.toString(),
        log.error || "",
        log.metadata ? JSON.stringify(log.metadata) : "",
      ]);

      const csvContent = [
        csvHeaders.join(","),
        ...csvRows.map((row) =>
          row.map((field) => `"${field.toString().replace(/"/g, '""')}"`).join(",")
        ),
      ].join("\n");

      return new NextResponse(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="audit-logs-${new Date().toISOString().split("T")[0]}.csv"`,
        },
      });
    }

    // Regular paginated response
    const skip = (page - 1) * limit;

    const [logs, totalCount] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
        },
        orderBy: { timestamp: "desc" },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      logs,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
      },
      // For backward compatibility
      currentPage: page,
      totalPages,
    });
  } catch (error) {
    console.error("Error fetching audit logs:", error);

    // Log the error
    try {
      await logAuditEvent({
        action: "AUDIT_LOGS_ACCESS_FAILED",
        resource: "/api/admin/audit-logs",
        ip: getClientIP(req),
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    } catch (logError) {
      console.error("Failed to log audit event:", logError);
    }

    return NextResponse.json(
      { error: "Failed to fetch audit logs" },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clean up old audit logs
export async function DELETE(req: NextRequest) {
  try {
    // Check admin authorization
    const authResult = await requireRole(["admin"]);
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const { user } = authResult;
    const { searchParams } = new URL(req.url);
    const daysToKeep = parseInt(searchParams.get("daysToKeep") || "90");

    if (daysToKeep < 30) {
      return NextResponse.json(
        { error: "Cannot delete logs newer than 30 days" },
        { status: 400 }
      );
    }

    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

    const deletedLogs = await prisma.auditLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate,
        },
      },
    });

    // Log the cleanup action
    await logAuditEvent({
      userId: user.id,
      action: "AUDIT_LOGS_CLEANUP",
      resource: "/api/admin/audit-logs",
      ip: getClientIP(req),
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
      metadata: {
        daysToKeep,
        deletedCount: deletedLogs.count,
        cutoffDate: cutoffDate.toISOString(),
      },
    });

    return NextResponse.json({
      success: true,
      deletedCount: deletedLogs.count,
      cutoffDate: cutoffDate.toISOString(),
    });
  } catch (error) {
    console.error("Error cleaning up audit logs:", error);

    // Log the error
    try {
      await logAuditEvent({
        action: "AUDIT_LOGS_CLEANUP_FAILED",
        resource: "/api/admin/audit-logs",
        ip: getClientIP(req),
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    } catch (logError) {
      console.error("Failed to log audit event:", logError);
    }

    return NextResponse.json(
      { error: "Failed to clean up audit logs" },
      { status: 500 }
    );
  }
}
