import { Suspense } from "react";
import { prisma } from "@/lib/prisma";
import { analytics } from "@/lib/analytics";
import DashboardLayout from "@/app/components/dashboard/DashboardLayout";
import StatCard, { StatIcons } from "@/app/components/dashboard/StatCard";
import InteractiveChart from "@/app/components/dashboard/InteractiveChart";
import QuickActions, {
  ActionIcons,
} from "@/app/components/dashboard/QuickActions";
import DataTable from "@/app/components/dashboard/DataTable";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

async function getDashboardData() {
  try {
    const [
      users,
      bookings,
      resorts,
      spaTreatments,
      bookingAnalytics,
      systemMetrics,
      bookingPatterns,
    ] = await Promise.all([
      prisma.user.findMany({
        select: { id: true, email: true, role: true, createdAt: true },
      }),
      prisma.booking.findMany({
        include: { resort: true, spaTreatment: true },
        orderBy: { createdAt: "desc" },
        take: 10,
      }),
      prisma.resort.findMany({
        select: { id: true, name: true, location: true, image: true },
      }),
      prisma.spaTreatment.findMany({
        select: { id: true, name: true, price: true, duration: true },
      }),
      analytics.getBookingAnalytics(),
      analytics.getSystemMetrics(),
      analytics.getBookingPatterns(),
    ]);

    return {
      users,
      bookings,
      resorts,
      spaTreatments,
      bookingAnalytics,
      systemMetrics,
      bookingPatterns,
      recentBookings: bookings,
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    throw new Error("Failed to load dashboard data");
  }
}

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

async function DashboardContent() {
  const data = await getDashboardData();

  // Enhanced data processing for charts
  const processChartData = () => {
    try {
      return {
        bookingStatusData: Object.entries(
          data.bookingAnalytics.bookingsByStatus
        ).map(([status, count]) => ({
          label: status.charAt(0) + status.slice(1).toLowerCase(),
          value: count,
          metadata: { status },
        })),
        bookingTypeData: Object.entries(
          data.bookingAnalytics.bookingsByType
        ).map(([type, count]) => ({
          label: type,
          value: count,
          metadata: { type },
        })),
        revenueData: data.bookingAnalytics.revenueByMonth.map((item) => ({
          label: item.month,
          value: item.revenue,
          metadata: { bookings: item.bookings },
        })),
        seasonalTrendsData:
          data.bookingAnalytics.seasonalTrends?.map((item) => ({
            label: item.period,
            value: item.bookings,
            metadata: { revenue: item.revenue },
          })) || [],
      };
    } catch (error) {
      console.error("Error processing chart data:", error);
      return {
        bookingStatusData: [],
        bookingTypeData: [],
        revenueData: [],
        seasonalTrendsData: [],
      };
    }
  };

  const chartData = processChartData();

  const quickActions = [
    {
      id: "add-resort",
      title: "Add Resort",
      description: "Create a new resort listing",
      icon: <ActionIcons.Add />,
      href: "/admin/resorts/create",
      color: "blue" as const,
    },
    {
      id: "add-spa",
      title: "Add Spa Treatment",
      description: "Create a new spa treatment",
      icon: <ActionIcons.Add />,
      href: "/admin/spa/create",
      color: "green" as const,
    },
    {
      id: "add-dining",
      title: "Add Dining Option",
      description: "Create a new dining option",
      icon: <ActionIcons.Add />,
      href: "/admin/dining/create",
      color: "yellow" as const,
    },
    {
      id: "add-experience",
      title: "Add Experience",
      description: "Create a new experience",
      icon: <ActionIcons.Add />,
      href: "/admin/experiences/create",
      color: "indigo" as const,
    },
    {
      id: "add-event",
      title: "Add Event",
      description: "Create a new event",
      icon: <ActionIcons.Add />,
      href: "/admin/events/create",
      color: "purple" as const,
    },
    {
      id: "add-offer",
      title: "Add Special Offer",
      description: "Create a new special offer",
      icon: <ActionIcons.Add />,
      href: "/admin/offers/create",
      color: "red" as const,
    },
    {
      id: "view-bookings",
      title: "View All Bookings",
      description: "Manage customer bookings",
      icon: <ActionIcons.View />,
      href: "/dashboard/bookings",
      color: "blue" as const,
    },
    {
      id: "security",
      title: "Security Dashboard",
      description: "Monitor system security",
      icon: <ActionIcons.Security />,
      href: "/admin/security",
      color: "slate" as const,
    },
  ];

  // Enhanced quick actions with accessibility
  const enhancedQuickActions = quickActions.map((action) => ({
    ...action,
    ariaLabel: `${action.title}: ${action.description}`,
  }));

  const recentBookingsColumns = [
    {
      key: "userEmail" as keyof (typeof data.bookings)[0],
      title: "Customer",
      sortable: true,
    },
    {
      key: "type" as keyof (typeof data.bookings)[0],
      title: "Type",
      render: (value: unknown, row: (typeof data.bookings)[0]) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            row.resort
              ? "bg-blue-100 text-blue-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {row.resort ? "Resort" : "Spa"}
        </span>
      ),
    },
    {
      key: "service" as keyof (typeof data.bookings)[0],
      title: "Service",
      render: (value: unknown, row: (typeof data.bookings)[0]) =>
        row.resort?.name || row.spaTreatment?.name || "N/A",
    },
    {
      key: "status" as keyof (typeof data.bookings)[0],
      title: "Status",
      render: (value: unknown, row: (typeof data.bookings)[0]) => {
        const status = value as string;
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              status === "CONFIRMED"
                ? "bg-green-100 text-green-800"
                : status === "PENDING"
                ? "bg-yellow-100 text-yellow-800"
                : status === "CANCELLED"
                ? "bg-red-100 text-red-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            {status}
          </span>
        );
      },
    },
    {
      key: "createdAt" as keyof (typeof data.bookings)[0],
      title: "Date",
      render: (value: Date) => new Date(value).toLocaleDateString(),
      sortable: true,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics with Enhanced Accessibility */}
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        role="region"
        aria-label="Key performance metrics"
      >
        <ErrorBoundary>
          <StatCard
            title="Total Users"
            value={data.users.length}
            icon={<StatIcons.Users />}
            color="blue"
            change={{
              value: 12,
              type: "increase",
              period: "last month",
              isPercentage: true,
            }}
            ariaLabel={`Total users: ${data.users.length}, increased by 12% compared to last month`}
            description="Total number of registered users in the system"
            onClick={() => console.log("Navigate to users page")}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Total Bookings"
            value={data.bookingAnalytics.totalBookings}
            icon={<StatIcons.Bookings />}
            color="green"
            change={{
              value: 8,
              type: "increase",
              period: "last week",
              isPercentage: true,
            }}
            ariaLabel={`Total bookings: ${data.bookingAnalytics.totalBookings}, increased by 8% compared to last week`}
            description="Total number of bookings across all services"
            onClick={() => console.log("Navigate to bookings page")}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Active Resorts"
            value={data.resorts.length}
            icon={<StatIcons.Resorts />}
            color="purple"
            ariaLabel={`Active resorts: ${data.resorts.length}`}
            description="Number of currently active resort listings"
            onClick={() => console.log("Navigate to resorts page")}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Spa Treatments"
            value={data.spaTreatments.length}
            icon={<StatIcons.Spa />}
            color="indigo"
            ariaLabel={`Spa treatments: ${data.spaTreatments.length}`}
            description="Number of available spa treatment options"
            onClick={() => console.log("Navigate to spa treatments page")}
          />
        </ErrorBoundary>
      </div>

      {/* System Performance with Enhanced Monitoring */}
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        role="region"
        aria-label="System performance metrics"
      >
        <ErrorBoundary>
          <StatCard
            title="Response Time"
            value={`${data.systemMetrics.responseTime}ms`}
            icon={<StatIcons.Performance />}
            color={data.systemMetrics.responseTime > 500 ? "red" : "green"}
            change={{
              value: data.systemMetrics.responseTime > 500 ? 15 : -5,
              type:
                data.systemMetrics.responseTime > 500 ? "increase" : "decrease",
              period: "last hour",
              isPercentage: true,
            }}
            ariaLabel={`Response time: ${
              data.systemMetrics.responseTime
            } milliseconds, ${
              data.systemMetrics.responseTime > 500 ? "above" : "within"
            } acceptable range`}
            description="Average API response time for the last hour"
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Error Rate"
            value={`${data.systemMetrics.errorRate}%`}
            icon={<StatIcons.Activity />}
            color={data.systemMetrics.errorRate > 5 ? "red" : "green"}
            change={{
              value: data.systemMetrics.errorRate > 5 ? 2 : -1,
              type: data.systemMetrics.errorRate > 5 ? "increase" : "decrease",
              period: "last hour",
              isPercentage: true,
            }}
            ariaLabel={`Error rate: ${data.systemMetrics.errorRate}%, ${
              data.systemMetrics.errorRate > 5 ? "above" : "within"
            } acceptable threshold`}
            description="Percentage of failed requests in the last hour"
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Active Users"
            value={data.systemMetrics.activeUsers}
            icon={<StatIcons.Users />}
            color="blue"
            change={{
              value: 3,
              type: "increase",
              period: "last 15 minutes",
              isPercentage: false,
            }}
            ariaLabel={`Active users: ${data.systemMetrics.activeUsers}, increased by 3 users in the last 15 minutes`}
            description="Number of users currently active on the platform"
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <StatCard
            title="Occupancy Rate"
            value={`${data.bookingAnalytics.occupancyRate.toFixed(1)}%`}
            icon={<StatIcons.Occupancy />}
            color={
              data.bookingAnalytics.occupancyRate > 80
                ? "green"
                : data.bookingAnalytics.occupancyRate > 60
                ? "yellow"
                : "red"
            }
            change={{
              value: 2.5,
              type: "increase",
              period: "last week",
              isPercentage: true,
            }}
            ariaLabel={`Occupancy rate: ${data.bookingAnalytics.occupancyRate.toFixed(
              1
            )}%, increased by 2.5% compared to last week`}
            description="Current occupancy rate across all resort properties"
          />
        </ErrorBoundary>
      </div>

      {/* Enhanced Charts with Interactivity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ErrorBoundary>
          <InteractiveChart
            data={chartData.bookingStatusData}
            type="pie"
            title="Bookings by Status"
            height={300}
            width={400}
            interactive={true}
            showLegend={true}
            ariaLabel="Interactive pie chart showing distribution of booking statuses"
            description="Shows the breakdown of bookings by their current status: confirmed, pending, cancelled, etc."
            onDataPointClick={(dataPoint) => {
              console.log("Clicked booking status:", dataPoint);
            }}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <InteractiveChart
            data={chartData.bookingTypeData}
            type="bar"
            title="Bookings by Type"
            height={300}
            width={400}
            interactive={true}
            showValues={true}
            showGrid={true}
            ariaLabel="Interactive bar chart showing bookings by service type"
            description="Compares the number of resort bookings versus spa treatment bookings"
            onDataPointClick={(dataPoint) => {
              console.log("Clicked booking type:", dataPoint);
            }}
          />
        </ErrorBoundary>
      </div>

      {/* Additional Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ErrorBoundary>
          <InteractiveChart
            data={chartData.revenueData}
            type="line"
            title="Revenue Trends"
            height={300}
            width={400}
            interactive={true}
            showValues={true}
            showGrid={true}
            ariaLabel="Line chart showing monthly revenue trends"
            description="Displays revenue trends over the past months with booking counts"
            onDataPointClick={(dataPoint) => {
              console.log("Clicked revenue data:", dataPoint);
            }}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <InteractiveChart
            data={chartData.seasonalTrendsData}
            type="bar"
            title="Seasonal Booking Trends"
            height={300}
            width={400}
            interactive={true}
            showValues={true}
            ariaLabel="Bar chart showing seasonal booking patterns"
            description="Shows booking patterns across different seasons of the year"
            onDataPointClick={(dataPoint) => {
              console.log("Clicked seasonal data:", dataPoint);
            }}
          />
        </ErrorBoundary>
      </div>

      {/* Enhanced Quick Actions */}
      <ErrorBoundary>
        <QuickActions
          title="Quick Actions"
          actions={enhancedQuickActions}
          columns={4}
          ariaLabel="Dashboard quick actions for common administrative tasks"
        />
      </ErrorBoundary>

      {/* Enhanced Recent Bookings Table */}
      <ErrorBoundary>
        <div role="region" aria-label="Recent bookings table">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Bookings
          </h3>
          <DataTable
            data={data.bookings}
            columns={recentBookingsColumns}
            searchable
            searchPlaceholder="Search bookings by customer email, service, or status..."
            pagination={{
              pageSize: 5,
              showSizeChanger: true,
            }}
            className="mt-6"
            emptyMessage="No recent bookings found"
            ariaLabel="Recent bookings data table"
            caption="Table showing the most recent customer bookings with their details and status"
            onRowClick={(booking) => {
              console.log("Navigate to booking details:", booking.id);
            }}
            selectable={true}
            onSelectionChange={(selectedBookings) => {
              console.log("Selected bookings:", selectedBookings.length);
            }}
          />
        </div>
      </ErrorBoundary>
    </div>
  );
}

export default function AdminDashboard() {
  return (
    <ErrorBoundary
      onError={(error) => {
        console.error("Dashboard error:", error);
        // Could send to error reporting service here
      }}
      showDetails={process.env.NODE_ENV === "development"}
    >
      <DashboardLayout
        title="Admin Dashboard"
        subtitle="Overview of system performance and key metrics"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Dashboard", current: true },
        ]}
      >
        <Suspense fallback={<DashboardSkeleton />}>
          <DashboardContent />
        </Suspense>
      </DashboardLayout>
    </ErrorBoundary>
  );
}
