/**
 * Format currency values for display
 */
export const formatCurrency = (value: number): string => {
  return `$${value.toLocaleString()}`;
};

/**
 * Format percentage values for display
 */
export const formatPercentage = (value: number): string => {
  return `${value}%`;
};

/**
 * Format date for chart display
 */
export const formatChartDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Format time for display
 */
export const formatTime = (dateString: string): string => {
  return new Date(dateString).toLocaleTimeString();
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

/**
 * Calculate percentage change
 */
export const calculatePercentageChange = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
};

/**
 * Get color classes for stat cards
 */
export const getStatCardColorClasses = (color: string) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    green: "bg-green-50 text-green-600 border-green-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200",
    orange: "bg-orange-50 text-orange-600 border-orange-200",
    red: "bg-red-50 text-red-600 border-red-200",
  };
  return colorClasses[color as keyof typeof colorClasses] || colorClasses.blue;
};

/**
 * Calculate chart bar width percentage
 */
export const calculateBarWidth = (value: number, maxValue: number): string => {
  if (maxValue === 0) return "0%";
  return `${(value / maxValue) * 100}%`;
};

/**
 * Validate analytics data
 */
export const validateAnalyticsData = (data: any): boolean => {
  return (
    data &&
    typeof data === "object" &&
    data.overview &&
    data.growth &&
    Array.isArray(data.topPerformingResorts) &&
    Array.isArray(data.bookingTrends) &&
    data.lastUpdated
  );
};

/**
 * Get trend indicator
 */
export const getTrendIndicator = (isPositive: boolean): string => {
  return isPositive ? "↗" : "↘";
};

/**
 * Get trend color class
 */
export const getTrendColorClass = (isPositive: boolean): string => {
  return isPositive ? "text-green-600" : "text-red-600";
};
