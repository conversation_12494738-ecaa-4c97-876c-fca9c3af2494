import { useState, useCallback } from "react";
import { useRealTimeData } from "@/app/components/dashboard/useRealTimeData";
import {
  SystemMetrics,
  DashboardConfig,
  RealTimeState,
  StatusColor,
  ChartDataPoint,
} from "@/app/types/dashboard";

interface UseDashboardProps {
  updateInterval?: number;
  config?: Partial<DashboardConfig>;
  onMetricsUpdate?: (metrics: SystemMetrics) => void;
  onError?: (error: Error) => void;
}

const defaultConfig: DashboardConfig = {
  updateInterval: 30000,
  enabled: true,
  autoRefresh: true,
  showCharts: true,
  showSystemHealth: true,
  thresholds: {
    responseTime: { good: 200, warning: 500 },
    errorRate: { good: 1, warning: 5 },
    cpuUsage: { good: 60, warning: 80 },
    memoryUsage: { good: 60, warning: 80 },
  },
};

export function useDashboard({
  updateInterval = 30000,
  config: userConfig = {},
  onMetricsUpdate,
  onError,
}: UseDashboardProps = {}) {
  const [isLive, setIsLive] = useState(true);
  const config = { ...defaultConfig, ...userConfig };

  // Mock data fetcher for demonstration
  const fetchLiveData = async (): Promise<SystemMetrics> => {
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Generate realistic mock data with some variation
    const baseResponseTime = 150;
    const variation = Math.random() * 100 - 50; // -50 to +50ms variation

    const metrics: SystemMetrics = {
      responseTime: Math.max(50, Math.round(baseResponseTime + variation)),
      errorRate: Math.max(0, Math.round(Math.random() * 2 * 100) / 100), // 0-2%
      activeUsers: Math.round(50 + Math.random() * 20), // 50-70 users
      databaseConnections: Math.round(10 + Math.random() * 5), // 10-15 connections
      memoryUsage: Math.round(60 + Math.random() * 20), // 60-80%
      cpuUsage: Math.round(30 + Math.random() * 40), // 30-70%
      uptime: 99.9,
      requestsPerMinute: Math.round(100 + Math.random() * 50), // 100-150 RPM
      errorCount: Math.round(Math.random() * 5), // 0-5 errors
      lastUpdated: new Date().toISOString(),
    };

    if (onMetricsUpdate) {
      onMetricsUpdate(metrics);
    }

    return metrics;
  };

  const {
    data: liveMetrics,
    loading,
    error,
    lastUpdated,
    isConnected,
    refresh,
    retry,
  } = useRealTimeData(fetchLiveData, {
    enabled: isLive && config.enabled,
    updateInterval: config.updateInterval,
    onUpdate: onMetricsUpdate,
    onError,
  });

  /**
   * Toggle live updates
   */
  const toggleLive = useCallback(() => {
    setIsLive((prev) => !prev);
  }, []);

  /**
   * Get status color based on value and thresholds
   */
  const getStatusColor = useCallback(
    (
      value: number,
      thresholds: { good: number; warning: number }
    ): StatusColor => {
      if (value <= thresholds.good) return "green";
      if (value <= thresholds.warning) return "yellow";
      return "red";
    },
    []
  );

  /**
   * Get metric status with color and message
   */
  const getMetricStatus = useCallback(
    (metricName: keyof SystemMetrics, value: number) => {
      const thresholds = config.thresholds;
      let status: StatusColor = "gray";
      let message = "";

      switch (metricName) {
        case "responseTime":
          status = getStatusColor(value, thresholds.responseTime);
          message =
            status === "green"
              ? "Excellent response time"
              : status === "yellow"
              ? "Acceptable response time"
              : "Poor response time";
          break;
        case "errorRate":
          status = getStatusColor(value, thresholds.errorRate);
          message =
            status === "green"
              ? "Low error rate"
              : status === "yellow"
              ? "Moderate error rate"
              : "High error rate";
          break;
        case "cpuUsage":
          status = getStatusColor(value, thresholds.cpuUsage);
          message =
            status === "green"
              ? "Normal CPU usage"
              : status === "yellow"
              ? "High CPU usage"
              : "Critical CPU usage";
          break;
        case "memoryUsage":
          status = getStatusColor(value, thresholds.memoryUsage);
          message =
            status === "green"
              ? "Normal memory usage"
              : status === "yellow"
              ? "High memory usage"
              : "Critical memory usage";
          break;
        default:
          status = "blue";
          message = "Metric value";
      }

      return { status, message };
    },
    [config.thresholds, getStatusColor]
  );

  /**
   * Generate chart data from live metrics
   */
  const generateChartData = useCallback((): ChartDataPoint[] => {
    if (!liveMetrics) return [];

    return [
      {
        label: "CPU",
        value: liveMetrics.cpuUsage,
        color: "#3B82F6",
        threshold: config.thresholds.cpuUsage,
      },
      {
        label: "Memory",
        value: liveMetrics.memoryUsage,
        color: "#10B981",
        threshold: config.thresholds.memoryUsage,
      },
      {
        label: "Response Time",
        value: Math.min(100, liveMetrics.responseTime / 5),
        color: "#F59E0B",
        threshold: { good: 40, warning: 100 }, // Normalized thresholds
      },
    ];
  }, [liveMetrics, config.thresholds]);

  /**
   * Get system health summary
   */
  const getSystemHealthSummary = useCallback(() => {
    if (!liveMetrics) return null;

    const cpuStatus = getMetricStatus("cpuUsage", liveMetrics.cpuUsage);
    const memoryStatus = getMetricStatus("memoryUsage", liveMetrics.memoryUsage);
    const responseStatus = getMetricStatus("responseTime", liveMetrics.responseTime);
    const errorStatus = getMetricStatus("errorRate", liveMetrics.errorRate);

    const statuses = [cpuStatus, memoryStatus, responseStatus, errorStatus];
    const criticalCount = statuses.filter((s) => s.status === "red").length;
    const warningCount = statuses.filter((s) => s.status === "yellow").length;

    let overallStatus: StatusColor = "green";
    let overallMessage = "All systems operational";

    if (criticalCount > 0) {
      overallStatus = "red";
      overallMessage = `${criticalCount} critical issue${criticalCount > 1 ? "s" : ""}`;
    } else if (warningCount > 0) {
      overallStatus = "yellow";
      overallMessage = `${warningCount} warning${warningCount > 1 ? "s" : ""}`;
    }

    return {
      status: overallStatus,
      message: overallMessage,
      details: {
        cpu: cpuStatus,
        memory: memoryStatus,
        response: responseStatus,
        error: errorStatus,
      },
    };
  }, [liveMetrics, getMetricStatus]);

  /**
   * Check if dashboard has critical alerts
   */
  const hasCriticalAlerts = useCallback(() => {
    const summary = getSystemHealthSummary();
    return summary?.status === "red";
  }, [getSystemHealthSummary]);

  /**
   * Get dashboard state
   */
  const getDashboardState = useCallback((): RealTimeState => {
    return {
      isLive,
      isConnected,
      loading,
      error,
      lastUpdated,
      metrics: liveMetrics,
    };
  }, [isLive, isConnected, loading, error, lastUpdated, liveMetrics]);

  return {
    // State
    metrics: liveMetrics,
    loading,
    error,
    lastUpdated,
    isConnected,
    isLive,
    config,

    // Actions
    toggleLive,
    refresh,
    retry,

    // Computed data
    chartData: generateChartData(),
    systemHealth: getSystemHealthSummary(),
    dashboardState: getDashboardState(),

    // Utilities
    getStatusColor,
    getMetricStatus,
    hasCriticalAlerts: hasCriticalAlerts(),

    // Status helpers
    isHealthy: !hasCriticalAlerts(),
    hasWarnings: getSystemHealthSummary()?.status === "yellow",
    isOperational: isConnected && !error,
  };
}
