# Codebase Refactoring Plan

## 🎯 **Objectives**

- Improve code organization and maintainability
- Break down large components into smaller, reusable modules
- Extract utility functions and custom hooks
- Create shared interfaces and type definitions
- Modularize API routes and business logic
- Maintain existing functionality, TypeScript types, error handling, and accessibility

## 📊 **Analysis Summary**

### **Files Requiring Refactoring (>200-300 lines):**

1. `app/components/BookingForm.tsx` (436 lines)
2. `app/components/dashboard/RealTimeDashboard.tsx` (328 lines)
3. `app/components/BookingManagement.tsx` (288 lines)
4. `app/components/ResortsWithFilters.tsx` (282 lines)
5. `app/api/bookings/route.ts` (244 lines)
6. `app/api/upload/route.ts` (300+ lines)

### **Areas for Improvement:**

- Complex form validation logic
- Repeated filtering patterns
- Large API route handlers
- Mixed concerns in components
- Duplicated utility functions

## 🔧 **Refactoring Strategy**

### **Phase 1: Extract Utility Functions and Hooks**

#### **1.1 Create Booking Utilities**

- **File:** `lib/booking-utils.ts`
- **Purpose:** Extract booking validation, availability checking, and email logic
- **Functions:**
  - `validateBookingDates()`
  - `checkResourceAvailability()`
  - `getBookingTypeInfo()`
  - `generateBookingEmailData()`

#### **1.2 Create Form Validation Hooks**

- **File:** `app/hooks/useFormValidation.ts`
- **Purpose:** Reusable form validation logic
- **Features:**
  - Generic validation hook
  - Error state management
  - Field-level validation

#### **1.3 Create Filter Utilities**

- **File:** `lib/filter-utils.ts`
- **Purpose:** Reusable filtering and search logic
- **Functions:**
  - `applySearchFilter()`
  - `applyLocationFilter()`
  - `updateURLParams()`

### **Phase 2: Break Down Large Components**

#### **2.1 Refactor BookingForm.tsx**

**Split into:**

- `app/components/booking/BookingForm.tsx` (main component)
- `app/components/booking/BookingFormFields.tsx` (form fields)
- `app/components/booking/AvailabilityChecker.tsx` (availability logic)
- `app/components/booking/BookingTypeSelector.tsx` (content type logic)
- `app/hooks/useBookingForm.ts` (form state management)

#### **2.2 Refactor RealTimeDashboard.tsx**

**Split into:**

- `app/components/dashboard/RealTimeDashboard.tsx` (main component)
- `app/components/dashboard/LiveMetricsGrid.tsx` (metrics display)
- `app/components/dashboard/SystemHealthPanel.tsx` (health indicators)
- `app/components/dashboard/DashboardControls.tsx` (controls)

#### **2.3 Refactor BookingManagement.tsx**

**Split into:**

- `app/components/booking/BookingManagement.tsx` (main component)
- `app/components/booking/BookingCard.tsx` (individual booking)
- `app/components/booking/BookingFilters.tsx` (filter controls)
- `app/hooks/useBookingManagement.ts` (state management)

#### **2.4 Refactor ResortsWithFilters.tsx**

**Split into:**

- `app/components/resorts/ResortsWithFilters.tsx` (main component)
- `app/components/resorts/ResortCard.tsx` (individual resort)
- `app/components/resorts/ResortFilters.tsx` (filter component)
- `app/hooks/useResortFilters.ts` (filter logic)

### **Phase 3: Modularize API Routes**

#### **3.1 Refactor Booking API**

**Split into:**

- `app/api/bookings/route.ts` (main handler)
- `lib/api/booking-service.ts` (business logic)
- `lib/api/booking-validation.ts` (validation logic)
- `lib/api/availability-service.ts` (availability checking)

#### **3.2 Refactor Upload API**

**Split into:**

- `app/api/upload/route.ts` (main handler)
- `lib/api/upload-service.ts` (upload logic)
- `lib/api/file-validation.ts` (file validation)
- `lib/api/cloudinary-service.ts` (cloudinary integration)

### **Phase 4: Create Shared Interfaces**

#### **4.1 Booking Types**

- **File:** `app/types/booking.ts`
- **Interfaces:** `BookingFormData`, `BookingValidation`, `AvailabilityCheck`

#### **4.2 Filter Types**

- **File:** `app/types/filters.ts`
- **Interfaces:** `FilterState`, `SearchParams`, `FilterOptions`

#### **4.3 Dashboard Types**

- **File:** `app/types/dashboard.ts`
- **Interfaces:** `MetricsData`, `DashboardConfig`, `RealTimeState`

### **Phase 5: Improve File Structure**

#### **5.1 Reorganize Components**

```
app/components/
├── booking/
│   ├── BookingForm.tsx
│   ├── BookingFormFields.tsx
│   ├── AvailabilityChecker.tsx
│   ├── BookingManagement.tsx
│   ├── BookingCard.tsx
│   └── BookingFilters.tsx
├── resorts/
│   ├── ResortsWithFilters.tsx
│   ├── ResortCard.tsx
│   └── ResortFilters.tsx
├── dashboard/
│   ├── RealTimeDashboard.tsx
│   ├── LiveMetricsGrid.tsx
│   ├── SystemHealthPanel.tsx
│   └── DashboardControls.tsx
└── shared/
    ├── forms/
    ├── filters/
    └── loading/
```

#### **5.2 Reorganize Utilities**

```
lib/
├── api/
│   ├── booking-service.ts
│   ├── upload-service.ts
│   ├── file-validation.ts
│   └── availability-service.ts
├── hooks/
│   ├── useFormValidation.ts
│   ├── useBookingForm.ts
│   ├── useBookingManagement.ts
│   └── useResortFilters.ts
├── utils/
│   ├── booking-utils.ts
│   ├── filter-utils.ts
│   ├── form-utils.ts
│   └── date-utils.ts
└── types/
    ├── booking.ts
    ├── filters.ts
    └── dashboard.ts
```

## 📅 **Implementation Timeline**

### **Week 1: Phase 1 - Extract Utilities** ✅ **COMPLETED**

- ✅ Create utility functions and custom hooks
  - ✅ `lib/utils/booking-utils.ts` - Booking validation, availability checking, and email logic
  - ✅ `app/hooks/useFormValidation.ts` - Reusable form validation logic
  - ✅ `lib/utils/filter-utils.ts` - Reusable filtering and search logic
- ✅ Extract common validation logic
- ✅ Create shared filter utilities

### **Week 2: Phase 2 - Break Down Components** 🔄 **IN PROGRESS**

- ✅ **Refactor BookingForm** - **COMPLETED**
  - ✅ `app/components/BookingForm.tsx` (111 lines, down from 436 lines)
  - ✅ `app/components/booking/BookingFormFields.tsx` - Form fields component
  - ✅ `app/components/booking/AvailabilityChecker.tsx` - Availability logic component
  - ✅ `app/hooks/useBookingForm.ts` - Form state management hook
  - ✅ `app/types/booking.ts` - Comprehensive booking type definitions
- ✅ **Refactor BookingManagement** - **COMPLETED**
  - ✅ `app/components/BookingManagement.tsx` (167 lines, down from 288 lines)
  - ✅ `app/components/booking/BookingCard.tsx` - Individual booking display component
  - ✅ `app/components/booking/BookingFilters.tsx` - Advanced filtering component
  - ✅ `app/hooks/useBookingManagement.ts` - Booking management state and logic
- ✅ **Refactor RealTimeDashboard** - **COMPLETED**
  - ✅ `app/components/dashboard/RealTimeDashboard.tsx` (137 lines, down from 328 lines)
  - ✅ `app/components/dashboard/LiveMetricsGrid.tsx` - Live metrics display component
  - ✅ `app/components/dashboard/SystemHealthPanel.tsx` - System health monitoring component
  - ✅ `app/components/dashboard/DashboardControls.tsx` - Dashboard control panel component
  - ✅ `app/hooks/useDashboard.ts` - Dashboard state management and data processing
  - ✅ `app/types/dashboard.ts` - Comprehensive dashboard type definitions
- ✅ **Refactor ResortsWithFilters** - **COMPLETED**
  - ✅ `app/components/ResortsWithFilters.tsx` (128 lines, down from 282 lines)
  - ✅ `app/components/resorts/ResortCard.tsx` - Resort display and grid components
  - ✅ `app/components/resorts/ResortFilters.tsx` - Advanced filtering and quick filters
  - ✅ `app/hooks/useResortFilters.ts` - Resort filtering and state management
- ✅ Create reusable sub-components - **COMPLETED**

### **Week 3: Phase 3 - Modularize API Routes**

- Extract business logic from API routes
- Create service layer for complex operations
- Improve error handling and validation

### **Week 4: Phase 4-5 - Types and Structure**

- Create shared type definitions
- Reorganize file structure
- Update imports and references
- Comprehensive testing

## ✅ **Success Criteria**

### **Code Quality Metrics:**

- [ ] No files exceed 200 lines (except main pages)
- [ ] No functions exceed 50 lines
- [ ] Single responsibility principle followed
- [ ] Reusable components created
- [ ] Shared utilities extracted

### **Functionality Preservation:**

- [ ] All existing features work correctly
- [ ] TypeScript types maintained
- [ ] Error handling preserved
- [ ] Accessibility features intact
- [ ] Performance not degraded

### **Maintainability Improvements:**

- [ ] Clear separation of concerns
- [ ] Reusable components and utilities
- [ ] Consistent patterns across codebase
- [ ] Improved testability
- [ ] Better developer experience

## 🧪 **Testing Strategy**

### **During Refactoring:**

1. **Component Testing:** Ensure each split component works independently
2. **Integration Testing:** Verify components work together correctly
3. **API Testing:** Test all API endpoints maintain functionality
4. **Type Checking:** Ensure TypeScript compilation succeeds
5. **Accessibility Testing:** Verify ARIA labels and keyboard navigation

### **Post-Refactoring:**

1. **End-to-End Testing:** Full user journey testing
2. **Performance Testing:** Ensure no performance regression
3. **Security Testing:** Verify security features remain intact
4. **Cross-browser Testing:** Ensure compatibility maintained

## 📝 **Notes**

- **Backward Compatibility:** All existing imports and APIs will be maintained
- **Incremental Approach:** Changes will be made incrementally to minimize risk
- **Documentation:** Each refactored component will include updated documentation
- **Code Review:** All changes will undergo thorough code review
- **Rollback Plan:** Each phase can be rolled back independently if issues arise
