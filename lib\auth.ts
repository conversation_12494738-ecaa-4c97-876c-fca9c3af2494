import { AuthOptions, Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "./prisma";
import { logAuditEvent } from "./security";
import bcrypt from "bcryptjs";
import console from "console";

// Helper function to extract IP from request context
function getIPFromContext(): string {
  // In NextAuth callbacks, we don't have direct access to the request
  // This is a limitation we'll note for future improvement
  return "auth-callback";
}

const sessionConfig = {
  strategy: "jwt" as const,
  maxAge: 24 * 60 * 60,
  updateAge: 60 * 60,
};

const cookieConfig = {
  sessionToken: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.session-token"
        : "next-auth.session-token",
    options: {
      httpOnly: true,
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
  callbackUrl: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.callback-url"
        : "next-auth.callback-url",
    options: {
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
  csrfToken: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Host-next-auth.csrf-token"
        : "next-auth.csrf-token",
    options: {
      httpOnly: true,
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
};

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: sessionConfig,
  cookies: cookieConfig,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
        },
      },
    }),
    CredentialsProvider({
      id: "credentials",
      name: "Email and Password",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          // Find user by email
          const user = await prisma.user.findUnique({
            where: { email: credentials.email.toLowerCase() },
            select: {
              id: true,
              email: true,
              name: true,
              password: true,
              role: true,
              isActive: true,
              emailVerified: true,
            },
          });

          if (!user || !user.password) {
            await logAuditEvent({
              action: "CREDENTIALS_SIGN_IN_FAILED",
              resource: "auth",
              ip: "unknown",
              userAgent: "unknown",
              success: false,
              error: "User not found or no password set",
            });
            throw new Error("CredentialsSignin");
          }

          // Check if account is active
          if (!user.isActive) {
            await logAuditEvent({
              userId: user.id,
              action: "CREDENTIALS_SIGN_IN_FAILED",
              resource: "auth",
              ip: "unknown",
              userAgent: "unknown",
              success: false,
              error: "Account disabled",
            });
            throw new Error("AccountDisabled");
          }

          // Check if email is verified
          if (!user.emailVerified) {
            await logAuditEvent({
              userId: user.id,
              action: "CREDENTIALS_SIGN_IN_FAILED",
              resource: "auth",
              ip: "unknown",
              userAgent: "unknown",
              success: false,
              error: "Email not verified",
            });
            throw new Error("EmailNotVerified");
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            await logAuditEvent({
              userId: user.id,
              action: "CREDENTIALS_SIGN_IN_FAILED",
              resource: "auth",
              ip: "unknown",
              userAgent: "unknown",
              success: false,
              error: "Invalid password",
            });
            throw new Error("CredentialsSignin");
          }

          // Successful authentication
          await logAuditEvent({
            userId: user.id,
            action: "CREDENTIALS_SIGN_IN_SUCCESS",
            resource: "auth",
            ip: "unknown",
            userAgent: "unknown",
            success: true,
          });

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
          };
        } catch (error) {
          console.error("Credentials authorization error:", error);

          if (error instanceof Error) {
            throw error; // Re-throw known errors
          }

          throw new Error("CredentialsSignin");
        }
      },
    }),
  ],
  pages: {
    signIn: "/login",
    error: "/auth/error",
    signOut: "/auth/signout",
  },
  callbacks: {
    async signIn({ user }): Promise<boolean> {
      try {
        // Log sign-in attempts
        await logAuditEvent({
          userId: user.id,
          action: "SIGN_IN_ATTEMPT",
          resource: "auth",
          ip: getIPFromContext(),
          userAgent: "auth-callback",
          success: true,
        });

        return true;
      } catch (error) {
        console.error("Sign-in error:", error);
        await logAuditEvent({
          userId: user.id,
          action: "SIGN_IN_FAILED",
          resource: "auth",
          ip: "unknown",
          userAgent: "unknown",
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return false;
      }
    },
    async session({ session, token }): Promise<Session> {
      if (session.user && token) {
        session.user.id = token.sub || "";
        session.user.role = token.role || "user";

        // Add session metadata for security
        session.sessionId = token.sessionId;
        session.issuedAt =
          typeof token.iat === "number" ? token.iat : undefined;
      }
      return session;
    },
    async jwt({ token, user, trigger }): Promise<JWT> {
      // Only fetch user data on sign-in or when explicitly triggered
      if (user || trigger === "update") {
        try {
          const userId = user?.id || token.sub;
          if (userId) {
            const dbUser = await prisma.user.findUnique({
              where: { id: userId },
              select: { role: true }, // Only select the role field for efficiency
            });

            // Validate role and provide fallback
            const validRoles = ["admin", "manager", "receptionist", "user"];
            const userRole = dbUser?.role || "user";
            token.role = validRoles.includes(userRole) ? userRole : "user";

            // Add session security metadata
            token.iat = Math.floor(Date.now() / 1000);
            token.sessionId = crypto.randomUUID();
          }
        } catch (error) {
          console.error("Error fetching user role:", error);

          token.role = "user";
        }
      }

      const now = Math.floor(Date.now() / 1000);
      if (token.iat && now - (token.iat as number) > sessionConfig.updateAge) {
        try {
          if (token.sub) {
            const dbUser = await prisma.user.findUnique({
              where: { id: token.sub },
              select: { role: true },
            });

            if (dbUser) {
              token.role = dbUser.role;
            }
          }
        } catch (error) {
          console.error("Token refresh error:", error);
        }
      }

      return token;
    },
    async redirect({ url, baseUrl }): Promise<string> {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, isNewUser }) {
      await logAuditEvent({
        userId: user.id,
        action: isNewUser ? "USER_REGISTERED" : "USER_SIGNED_IN",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
    async signOut({ token, session }) {
      await logAuditEvent({
        userId: token?.sub || session?.user?.id,
        action: "USER_SIGNED_OUT",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
    async createUser({ user }) {
      await logAuditEvent({
        userId: user.id,
        action: "USER_CREATED",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
  },
  debug: process.env.NODE_ENV === "development",
};
