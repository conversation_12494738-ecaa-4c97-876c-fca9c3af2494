"use client";

import React from "react";
import { DashboardProps } from "@/app/types/dashboard";
import { useDashboard } from "@/app/hooks/useDashboard";
import LiveMetricsGrid, {
  LiveMetricsGridSkeleton,
  LiveMetricsGridError,
} from "./LiveMetricsGrid";
import SystemHealthPanel, {
  SystemHealthPanelSkeleton,
  SystemHealthPanelError,
} from "./SystemHealthPanel";
import DashboardControls, {
  DashboardControlsSkeleton,
} from "./DashboardControls";
import InteractiveChart from "./InteractiveChart";
import ErrorBoundary from "./ErrorBoundary";

export default function RealTimeDashboard({
  updateInterval = 30000, // 30 seconds
  className = "",
  config,
  onMetricsUpdate,
  onError,
}: DashboardProps) {
  const {
    metrics,
    loading,
    error,
    lastUpdated,
    isConnected,
    isLive,
    config: dashboardConfig,
    toggleLive,
    refresh,
    retry,
    chartData,
  } = useDashboard({
    updateInterval,
    config,
    onMetricsUpdate,
    onError,
  });

  return (
    <ErrorBoundary>
      <div className={`space-y-6 ${className}`}>
        {/* Dashboard Controls */}
        {loading && !metrics ? (
          <DashboardControlsSkeleton />
        ) : (
          <DashboardControls
            isLive={isLive}
            isConnected={isConnected}
            loading={loading}
            error={error as Error | null}
            lastUpdated={lastUpdated}
            onToggleLive={toggleLive}
            onRefresh={refresh}
            onRetry={retry}
          />
        )}

        {/* Live Metrics */}
        {loading && !metrics ? (
          <LiveMetricsGridSkeleton />
        ) : error && !metrics ? (
          <LiveMetricsGridError error={error.message} onRetry={retry} />
        ) : metrics ? (
          <LiveMetricsGrid
            metrics={metrics}
            loading={loading}
            error={error?.message}
            thresholds={dashboardConfig.thresholds}
          />
        ) : null}

        {/* Charts and System Health */}
        {metrics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Interactive Chart */}
            <InteractiveChart
              data={chartData}
              type="bar"
              title="System Resource Usage"
              height={300}
              width={400}
              interactive={true}
              showValues={true}
              showGrid={true}
              loading={loading}
              error={error?.message}
              ariaLabel="Real-time system resource usage chart"
              description="Shows current CPU, memory usage, and normalized response time"
            />

            {/* System Health Panel */}
            {loading && !metrics ? (
              <SystemHealthPanelSkeleton />
            ) : error && !metrics ? (
              <SystemHealthPanelError error={error.message} onRetry={retry} />
            ) : (
              <SystemHealthPanel
                metrics={metrics}
                loading={loading}
                error={error?.message}
              />
            )}
          </div>
        )}

        {/* Global Error State */}
        {error && !metrics && !loading && (
          <div
            className="bg-red-50 border border-red-200 rounded-lg p-6 text-center"
            role="alert"
          >
            <div className="text-red-400 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">
              Dashboard Connection Error
            </h3>
            <p className="text-red-600 mb-4">{error.message}</p>
            <button
              type="button"
              onClick={retry}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
}
