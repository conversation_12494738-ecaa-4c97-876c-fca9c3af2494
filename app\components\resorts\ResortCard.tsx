"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Resort } from "@/app/types/resort";

interface ResortCardProps {
  resort: Resort;
  showDescription?: boolean;
  showLocation?: boolean;
  className?: string;
  onClick?: (resort: Resort) => void;
}

export default function ResortCard({
  resort,
  showDescription = true,
  showLocation = true,
  className = "",
  onClick,
}: ResortCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(resort);
    }
  };

  const cardContent = (
    <div
      className={`bg-white rounded-xl shadow hover:shadow-md transition-shadow duration-200 group ${className}`}
      onClick={handleClick}
    >
      <div className="relative overflow-hidden rounded-t-xl">
        <Image
          src={resort.image}
          alt={resort.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
          width={300}
          height={200}
          loading="lazy"
        />
        {showLocation && resort.location && (
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
            {resort.location}
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-center font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
          {resort.name}
        </h3>
        {showDescription && resort.description && (
          <p className="text-sm text-gray-600 mt-2 line-clamp-2">
            {resort.description.length > 100
              ? `${resort.description.substring(0, 100)}...`
              : resort.description}
          </p>
        )}
      </div>
    </div>
  );

  if (onClick) {
    return (
      <button
        type="button"
        className="block w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-xl"
        aria-label={`View details for ${resort.name}`}
      >
        {cardContent}
      </button>
    );
  }

  return (
    <Link
      href={`/resorts/${resort.slug}`}
      className="block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-xl"
      aria-label={`View details for ${resort.name}`}
    >
      {cardContent}
    </Link>
  );
}

// Skeleton loader for resort card
export function ResortCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`bg-white rounded-xl shadow animate-pulse ${className}`} aria-hidden="true">
      <div className="relative overflow-hidden rounded-t-xl">
        <div className="w-full h-48 bg-gray-300"></div>
        <div className="absolute bottom-2 left-2 bg-gray-400 h-5 w-16 rounded"></div>
      </div>
      <div className="p-4">
        <div className="h-5 bg-gray-300 rounded w-3/4 mx-auto mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-full mb-1"></div>
        <div className="h-4 bg-gray-300 rounded w-2/3"></div>
      </div>
    </div>
  );
}

// Compact resort card for smaller spaces
export function CompactResortCard({
  resort,
  className = "",
  onClick,
}: {
  resort: Resort;
  className?: string;
  onClick?: (resort: Resort) => void;
}) {
  const handleClick = () => {
    if (onClick) {
      onClick(resort);
    }
  };

  const cardContent = (
    <div
      className={`bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 group ${className}`}
      onClick={handleClick}
    >
      <div className="flex items-center p-4">
        <div className="relative overflow-hidden rounded-lg flex-shrink-0">
          <Image
            src={resort.image}
            alt={resort.name}
            className="w-16 h-16 object-cover group-hover:scale-105 transition-transform duration-200"
            width={64}
            height={64}
            loading="lazy"
          />
        </div>
        <div className="ml-4 flex-1">
          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {resort.name}
          </h3>
          {resort.location && (
            <p className="text-sm text-gray-600">{resort.location}</p>
          )}
        </div>
      </div>
    </div>
  );

  if (onClick) {
    return (
      <button
        type="button"
        className="block w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
        aria-label={`View details for ${resort.name}`}
      >
        {cardContent}
      </button>
    );
  }

  return (
    <Link
      href={`/resorts/${resort.slug}`}
      className="block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
      aria-label={`View details for ${resort.name}`}
    >
      {cardContent}
    </Link>
  );
}

// Resort grid component
export function ResortGrid({
  resorts,
  loading = false,
  className = "",
  cardProps = {},
}: {
  resorts: Resort[];
  loading?: boolean;
  className?: string;
  cardProps?: Partial<ResortCardProps>;
}) {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 ${className}`}>
        {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
          <ResortCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 ${className}`}>
      {resorts.map((resort) => (
        <ResortCard key={resort.id} resort={resort} {...cardProps} />
      ))}
    </div>
  );
}

// Empty state for no resorts
export function ResortEmptyState({
  title = "No Resorts Found",
  description = "Try adjusting your search criteria.",
  actionLabel,
  onAction,
  className = "",
}: {
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="text-gray-400 text-6xl mb-4">🔍</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      {actionLabel && onAction && (
        <button
          type="button"
          onClick={onAction}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          {actionLabel}
        </button>
      )}
    </div>
  );
}
