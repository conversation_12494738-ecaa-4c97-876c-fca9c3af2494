"use client";

import React from "react";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";
import { CheckInOutManagerProps } from "@/lib/types/guest-types";
import { useGuestManagement } from "@/app/hooks/guest/useGuestManagement";
import GuestFilters from "./guest/GuestFilters";
import GuestList from "./guest/GuestList";

export default function CheckInOutManager({
  className = "",
}: CheckInOutManagerProps) {
  const {
    loading,
    error,
    processingGuests,
    selectedDate,
    filter,
    handleCheckIn,
    handleCheckOut,
    setSelectedDate,
    setFilter,
    filteredGuests,
    fetchGuests,
  } = useGuestManagement();

  return (
    <ErrorBoundary>
      <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Check-in / Check-out Manager
              </h2>
              <p className="text-gray-600 mt-1">
                Manage guest arrivals and departures
              </p>
            </div>
            <GuestFilters
              selectedDate={selectedDate}
              filter={filter}
              onDateChange={setSelectedDate}
              onFilterChange={setFilter}
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <GuestList
            guests={filteredGuests}
            loading={loading}
            error={error}
            processingGuests={processingGuests}
            onCheckIn={handleCheckIn}
            onCheckOut={handleCheckOut}
            onRetry={fetchGuests}
          />
        </div>
      </div>
    </ErrorBoundary>
  );
}
