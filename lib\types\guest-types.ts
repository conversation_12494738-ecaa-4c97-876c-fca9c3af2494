export interface Guest {
  id: string;
  userEmail: string;
  userName?: string;
  userPhone?: string;
  checkIn: string;
  checkOut?: string;
  status: "PENDING" | "CONFIRMED" | "CHECKED_IN" | "CHECKED_OUT" | "CANCELLED";
  resort?: { name: string; location: string } | null;
  spaTreatment?: { name: string; duration: number } | null;
  notes?: string;
  roomNumber?: string;
  specialRequests?: string;
}

export interface CheckInOutManagerProps {
  className?: string;
}

export interface GuestCardProps {
  guest: Guest;
  isProcessing: boolean;
  onCheckIn: (guestId: string) => void;
  onCheckOut: (guestId: string) => void;
}

export interface GuestFiltersProps {
  selectedDate: string;
  filter: GuestFilterType;
  onDateChange: (date: string) => void;
  onFilterChange: (filter: GuestFilterType) => void;
}

export interface GuestListProps {
  guests: Guest[];
  loading: boolean;
  error: string | null;
  processingGuests: Set<string>;
  onCheckIn: (guestId: string) => void;
  onCheckOut: (guestId: string) => void;
  onRetry: () => void;
}

export interface CheckInOutActionsProps {
  guest: Guest;
  isProcessing: boolean;
  onCheckIn: () => void;
  onCheckOut: () => void;
}

export type GuestFilterType = "all" | "pending" | "checked_in" | "ready_checkout";

export interface GuestManagementState {
  guests: Guest[];
  loading: boolean;
  error: string | null;
  processingGuests: Set<string>;
  selectedDate: string;
  filter: GuestFilterType;
}

export interface GuestManagementActions {
  fetchGuests: () => Promise<void>;
  handleCheckIn: (guestId: string) => Promise<void>;
  handleCheckOut: (guestId: string) => Promise<void>;
  setSelectedDate: (date: string) => void;
  setFilter: (filter: GuestFilterType) => void;
  filteredGuests: Guest[];
}
