"use client";

import React from "react";
import { Calendar, User, MapPin, Phone, Mail } from "lucide-react";
import { GuestCardProps } from "@/lib/types/guest-types";
import { 
  getStatusBadge, 
  formatDate, 
  getGuestDisplayName, 
  getLocationDisplay 
} from "@/lib/utils/guest-utils";
import CheckInOutActions from "./CheckInOutActions";

export default function GuestCard({
  guest,
  isProcessing,
  onCheckIn,
  onCheckOut,
}: GuestCardProps) {
  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* Guest Header */}
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-gray-500" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">
                {getGuestDisplayName(guest)}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-1" />
                  {guest.userEmail}
                </div>
                {guest.userPhone && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-1" />
                    {guest.userPhone}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Guest Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-3">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Check-in: {formatDate(guest.checkIn)}</span>
            </div>
            {guest.checkOut && (
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="w-4 h-4 mr-2" />
                <span>Check-out: {formatDate(guest.checkOut)}</span>
              </div>
            )}
            {(guest.resort || guest.spaTreatment) && (
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="w-4 h-4 mr-2" />
                <span>{getLocationDisplay(guest)}</span>
              </div>
            )}
          </div>

          {/* Notes */}
          {(guest.notes || guest.specialRequests) && (
            <div className="text-sm text-gray-600 mb-3">
              <strong>Notes:</strong> {guest.notes || guest.specialRequests}
            </div>
          )}

          {/* Status and Actions */}
          <div className="flex items-center justify-between">
            <div>{getStatusBadge(guest.status)}</div>
            <CheckInOutActions
              guest={guest}
              isProcessing={isProcessing}
              onCheckIn={() => onCheckIn(guest.id)}
              onCheckOut={() => onCheckOut(guest.id)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
