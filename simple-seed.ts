console.log("Starting simple seed test...");

import { prisma } from "./lib/prisma";

async function simpleSeed() {
  try {
    console.log("Testing database connection...");
    const userCount = await prisma.user.count();
    console.log("Current user count:", userCount);
    
    console.log("Creating a test user...");
    const testUser = await prisma.user.create({
      data: {
        name: "Test User",
        email: "<EMAIL>",
        role: "user",
        isActive: true,
      },
    });
    console.log("Created user:", testUser.name);
    
    console.log("Cleaning up test user...");
    await prisma.user.delete({
      where: { id: testUser.id },
    });
    console.log("Test completed successfully!");
    
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleSeed();
