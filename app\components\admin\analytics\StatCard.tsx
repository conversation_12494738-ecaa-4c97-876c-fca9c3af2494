"use client";

import React from "react";
import { StatCardProps } from "@/lib/types/analytics-types";
import { 
  getStatCardColorClasses, 
  getTrendIndicator, 
  getTrendColorClass 
} from "@/lib/utils/analytics-utils";

export default function StatCard({
  title,
  value,
  subtitle,
  trend,
  icon,
  color = "blue",
}: StatCardProps) {
  const colorClasses = getStatCardColorClasses(color);

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
          {trend && (
            <div
              className={`flex items-center mt-2 text-sm ${getTrendColorClass(
                trend.isPositive
              )}`}
            >
              <span className="mr-1">{getTrendIndicator(trend.isPositive)}</span>
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg border ${colorClasses}`}>
          {icon}
        </div>
      </div>
    </div>
  );
}
