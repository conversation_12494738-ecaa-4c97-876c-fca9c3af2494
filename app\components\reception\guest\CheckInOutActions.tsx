"use client";

import React from "react";
import { CheckCir<PERSON>, XCircle } from "lucide-react";
import { CheckInOutActionsProps } from "@/lib/types/guest-types";
import { canCheckIn, canCheckOut } from "@/lib/utils/guest-utils";

export default function CheckInOutActions({
  guest,
  isProcessing,
  onCheckIn,
  onCheckOut,
}: CheckInOutActionsProps) {
  return (
    <div className="flex items-center space-x-2">
      {canCheckIn(guest) && (
        <button
          type="button"
          onClick={onCheckIn}
          disabled={isProcessing}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
          aria-label={`Check in ${guest.userEmail}`}
        >
          <CheckCircle className="w-4 h-4 mr-2" />
          {isProcessing ? "Processing..." : "Check In"}
        </button>
      )}
      
      {canCheckOut(guest) && (
        <button
          type="button"
          onClick={onCheckOut}
          disabled={isProcessing}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
          aria-label={`Check out ${guest.userEmail}`}
        >
          <XCircle className="w-4 h-4 mr-2" />
          {isProcessing ? "Processing..." : "Check Out"}
        </button>
      )}
    </div>
  );
}
