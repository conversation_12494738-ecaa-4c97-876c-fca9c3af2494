"use client";

import React from "react";
import { AnalyticsGridProps } from "@/lib/types/analytics-types";
import { formatCurrency, formatPercentage, formatTime, formatDate } from "@/lib/utils/analytics-utils";
import StatCard from "./StatCard";

export default function AnalyticsGrid({ data }: AnalyticsGridProps) {
  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Resorts"
          value={data.overview.totalResorts}
          icon={<span className="text-xl">🏨</span>}
          color="blue"
        />
        <StatCard
          title="Total Bookings"
          value={data.overview.totalBookings}
          subtitle="All time"
          trend={{
            value: data.growth.bookingGrowthRate,
            isPositive: data.growth.bookingGrowthRate >= 0,
          }}
          icon={<span className="text-xl">📅</span>}
          color="green"
        />
        <StatCard
          title="Total Revenue"
          value={formatCurrency(data.overview.totalRevenue)}
          subtitle="From confirmed bookings"
          icon={<span className="text-xl">💰</span>}
          color="purple"
        />
        <StatCard
          title="Occupancy Rate"
          value={formatPercentage(data.overview.averageOccupancy)}
          subtitle="Last 30 days"
          icon={<span className="text-xl">📊</span>}
          color="orange"
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Rooms"
          value={`${data.overview.activeRooms}/${data.overview.totalRooms}`}
          subtitle="Available rooms"
          icon={<span className="text-xl">🛏️</span>}
          color="blue"
        />
        <StatCard
          title="This Month"
          value={data.growth.bookingsThisMonth}
          subtitle="Bookings"
          icon={<span className="text-xl">📈</span>}
          color="green"
        />
        <StatCard
          title="Average Rating"
          value={data.overview.averageRating}
          subtitle={`From ${data.overview.totalReviews} reviews`}
          icon={<span className="text-xl">⭐</span>}
          color="orange"
        />
        <StatCard
          title="Last Updated"
          value={formatTime(data.lastUpdated)}
          subtitle={formatDate(data.lastUpdated)}
          icon={<span className="text-xl">🔄</span>}
          color="purple"
        />
      </div>
    </div>
  );
}
