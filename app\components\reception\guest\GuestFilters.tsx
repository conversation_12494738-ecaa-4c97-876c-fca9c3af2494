"use client";

import React from "react";
import { GuestFiltersProps, GuestFilterType } from "@/lib/types/guest-types";
import { getFilterDisplayName } from "@/lib/utils/guest-utils";

const filterOptions: GuestFilterType[] = ["all", "pending", "checked_in", "ready_checkout"];

export default function GuestFilters({
  selectedDate,
  filter,
  onDateChange,
  onFilterChange,
}: GuestFiltersProps) {
  return (
    <div className="flex items-center space-x-4">
      <div>
        <label
          htmlFor="date-select"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Date
        </label>
        <input
          id="date-select"
          type="date"
          value={selectedDate}
          onChange={(e) => onDateChange(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Select date for guest management"
        />
      </div>
      
      <div>
        <label
          htmlFor="filter-select"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Filter
        </label>
        <select
          id="filter-select"
          value={filter}
          onChange={(e) => onFilterChange(e.target.value as GuestFilterType)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Filter guests by status"
        >
          {filterOptions.map((option) => (
            <option key={option} value={option}>
              {getFilterDisplayName(option)}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
