"use client";

import React from "react";
import { SystemHealthPanelProps, StatusColor } from "@/app/types/dashboard";

export default function SystemHealthPanel({
  metrics,
  loading,
  error,
  className = "",
}: SystemHealthPanelProps) {
  const getProgressColor = (value: number): string => {
    if (value > 80) return "bg-red-500";
    if (value > 60) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStatusIcon = (value: number) => {
    if (value > 80) {
      return (
        <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
      );
    }
    if (value > 60) {
      return (
        <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
          clipRule="evenodd"
        />
      </svg>
    );
  };

  if (loading) {
    return <SystemHealthPanelSkeleton className={className} />;
  }

  if (error) {
    return <SystemHealthPanelError error={error} className={className} />;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
        <div className="flex items-center space-x-1">
          {getStatusIcon(Math.max(metrics.cpuUsage, metrics.memoryUsage))}
          <span className="text-sm text-gray-600">
            {Math.max(metrics.cpuUsage, metrics.memoryUsage) > 80
              ? "Critical"
              : Math.max(metrics.cpuUsage, metrics.memoryUsage) > 60
              ? "Warning"
              : "Healthy"}
          </span>
        </div>
      </div>

      <div className="space-y-4">
        {/* CPU Usage */}
        <HealthMetric
          label="CPU Usage"
          value={metrics.cpuUsage}
          unit="%"
          color={getProgressColor(metrics.cpuUsage)}
          icon={getStatusIcon(metrics.cpuUsage)}
        />

        {/* Memory Usage */}
        <HealthMetric
          label="Memory Usage"
          value={metrics.memoryUsage}
          unit="%"
          color={getProgressColor(metrics.memoryUsage)}
          icon={getStatusIcon(metrics.memoryUsage)}
        />

        {/* Database Connections */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
            <span className="text-sm text-gray-600">Database Connections</span>
          </div>
          <span className="text-sm font-medium">
            {metrics.databaseConnections}/20
          </span>
        </div>

        {/* Uptime */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm text-gray-600">Uptime</span>
          </div>
          <span className="text-sm font-medium text-green-600">
            {metrics.uptime}%
          </span>
        </div>

        {/* Error Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {metrics.errorCount > 0 ? (
              <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            <span className="text-sm text-gray-600">Recent Errors</span>
          </div>
          <span
            className={`text-sm font-medium ${
              metrics.errorCount > 0 ? "text-red-600" : "text-green-600"
            }`}
          >
            {metrics.errorCount}
          </span>
        </div>
      </div>
    </div>
  );
}

// Health metric component
interface HealthMetricProps {
  label: string;
  value: number;
  unit: string;
  color: string;
  icon: React.ReactNode;
}

function HealthMetric({ label, value, unit, color, icon }: HealthMetricProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        {icon}
        <span className="text-sm text-gray-600">{label}</span>
      </div>
      <div className="flex items-center space-x-2">
        <div className="w-24 bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-500 ${color}`}
            style={{ width: `${value}%` }}
            aria-label={`${label}: ${value}${unit}`}
          />
        </div>
        <span className="text-sm font-medium w-10 text-right">
          {value}{unit}
        </span>
      </div>
    </div>
  );
}

// Skeleton loader
export function SystemHealthPanelSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 animate-pulse ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="h-5 bg-gray-300 rounded w-32"></div>
        <div className="h-4 bg-gray-300 rounded w-16"></div>
      </div>
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-24"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-24 h-2 bg-gray-300 rounded-full"></div>
              <div className="h-4 bg-gray-300 rounded w-10"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Error state
export function SystemHealthPanelError({
  error,
  onRetry,
  className = "",
}: {
  error: string;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-red-800">System Health Error</h3>
        <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
      </div>
      <p className="text-red-600 text-sm mb-4">{error}</p>
      {onRetry && (
        <button
          type="button"
          onClick={onRetry}
          className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      )}
    </div>
  );
}
