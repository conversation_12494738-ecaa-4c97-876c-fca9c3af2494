import { useState, useEffect, useCallback } from "react";
import { AnalyticsData, UseAnalyticsDataReturn } from "@/lib/types/analytics-types";
import { validateAnalyticsData } from "@/lib/utils/analytics-utils";

interface UseAnalyticsDataProps {
  refreshInterval?: number;
  autoRefresh?: boolean;
}

export function useAnalyticsData({
  refreshInterval = 5 * 60 * 1000, // 5 minutes
  autoRefresh = true,
}: UseAnalyticsDataProps = {}): UseAnalyticsDataReturn {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/admin/analytics/resorts");
      
      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.status}`);
      }

      const analyticsData = await response.json();

      // Validate the data structure
      if (!validateAnalyticsData(analyticsData)) {
        throw new Error("Invalid analytics data structure");
      }

      setData(analyticsData);
    } catch (err) {
      console.error("Error fetching analytics:", err);
      setError(err instanceof Error ? err.message : "Failed to load analytics data");
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Initial fetch
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Auto-refresh interval
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchAnalytics, refreshInterval);
    return () => clearInterval(interval);
  }, [fetchAnalytics, refreshInterval, autoRefresh]);

  return {
    data,
    loading,
    error,
    refresh,
  };
}
