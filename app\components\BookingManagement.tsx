"use client";

import React from "react";
import { BookingManagementProps } from "@/app/types/booking";
import { useBookingManagement } from "@/app/hooks/useBookingManagement";
import BookingCard, {
  BookingCardSkeleton,
  BookingEmptyState,
} from "./booking/BookingCard";
import BookingFilters, { QuickFilters } from "./booking/BookingFilters";
import { createFilterSummary } from "@/lib/utils/filter-utils";

export default function BookingManagement({
  userBookingsOnly = false,
  showFilters = true,
  showActions = true,
  onBookingUpdate,
  className = "",
}: BookingManagementProps) {
  const {
    bookings,
    allBookings,
    filters,
    loading,
    error,
    updatingBooking,
    stats,
    updateBookingStatus,
    updateFilters,
    clearFilters,
    refresh,
    isEmpty,
    hasFilters,
  } = useBookingManagement({ userBookingsOnly });

  const handleStatusUpdate = async (bookingId: string, status: any) => {
    await updateBookingStatus(bookingId, status);
    if (onBookingUpdate) {
      const updatedBooking = bookings.find((b) => b.id === bookingId);
      if (updatedBooking) {
        onBookingUpdate(updatedBooking);
      }
    }
  };

  const handleCancel = (bookingId: string) => {
    handleStatusUpdate(bookingId, "CANCELLED");
  };

  // Loading state
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-300 rounded w-48 animate-pulse"></div>
          <div className="flex space-x-2">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="h-8 bg-gray-300 rounded w-20 animate-pulse"
              ></div>
            ))}
          </div>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <BookingCardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="text-center py-8">
          <div className="text-red-400 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Bookings
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            type="button"
            onClick={refresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {userBookingsOnly ? "My Bookings" : "All Bookings"}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {createFilterSummary(filters, allBookings.length, bookings.length)}
          </p>
        </div>

        <div className="flex items-center gap-4">
          <button
            type="button"
            onClick={refresh}
            className="text-sm text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="Refresh bookings"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Quick Filters */}
      {showFilters && <QuickFilters onFilterChange={updateFilters} />}

      {/* Advanced Filters */}
      {showFilters && (
        <BookingFilters
          filters={filters}
          onFiltersChange={updateFilters}
          onClearFilters={clearFilters}
          showAdvanced={!userBookingsOnly}
        />
      )}

      {/* Bookings List */}
      {isEmpty ? (
        <BookingEmptyState
          title={
            hasFilters ? "No bookings match your filters" : "No bookings found"
          }
          description={
            hasFilters
              ? "Try adjusting your search criteria or clear the filters."
              : userBookingsOnly
              ? "You haven't made any bookings yet."
              : "No bookings have been made yet."
          }
          actionLabel={hasFilters ? "Clear Filters" : undefined}
          onAction={hasFilters ? clearFilters : undefined}
        />
      ) : (
        <div className="grid gap-4">
          {bookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              showActions={showActions}
              showCustomerInfo={!userBookingsOnly}
              onStatusUpdate={handleStatusUpdate}
              onCancel={handleCancel}
            />
          ))}
        </div>
      )}
    </div>
  );
}
