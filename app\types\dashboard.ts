// System metrics interface
export interface SystemMetrics {
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseConnections: number;
  memoryUsage: number;
  cpuUsage: number;
  uptime: number;
  requestsPerMinute: number;
  errorCount: number;
  lastUpdated: string;
}

// Dashboard configuration
export interface DashboardConfig {
  updateInterval: number;
  enabled: boolean;
  autoRefresh: boolean;
  showCharts: boolean;
  showSystemHealth: boolean;
  thresholds: {
    responseTime: { good: number; warning: number };
    errorRate: { good: number; warning: number };
    cpuUsage: { good: number; warning: number };
    memoryUsage: { good: number; warning: number };
  };
}

// Real-time state
export interface RealTimeState {
  isLive: boolean;
  isConnected: boolean;
  loading: boolean;
  error: Error | null;
  lastUpdated: Date | null;
  metrics: SystemMetrics | null;
}

// Dashboard props
export interface DashboardProps {
  updateInterval?: number;
  className?: string;
  config?: Partial<DashboardConfig>;
  onMetricsUpdate?: (metrics: SystemMetrics) => void;
  onError?: (error: Error) => void;
}

// Live metrics grid props
export interface LiveMetricsGridProps {
  metrics: SystemMetrics;
  loading: boolean;
  error?: string;
  thresholds?: DashboardConfig["thresholds"];
  className?: string;
}

// System health panel props
export interface SystemHealthPanelProps {
  metrics: SystemMetrics;
  loading: boolean;
  error?: string;
  className?: string;
}

// Dashboard controls props
export interface DashboardControlsProps {
  isLive: boolean;
  isConnected: boolean;
  loading: boolean;
  error: Error | null;
  lastUpdated: Date | null;
  onToggleLive: () => void;
  onRefresh: () => void;
  onRetry: () => void;
  className?: string;
}

// Chart data interface
export interface ChartDataPoint {
  label: string;
  value: number;
  color: string;
  threshold?: {
    good: number;
    warning: number;
  };
}

// Status color type
export type StatusColor = "green" | "yellow" | "red" | "blue" | "purple" | "gray";

// Metric status
export interface MetricStatus {
  value: number;
  status: StatusColor;
  message?: string;
  trend?: "up" | "down" | "stable";
}

// Dashboard alert
export interface DashboardAlert {
  id: string;
  type: "error" | "warning" | "info" | "success";
  title: string;
  message: string;
  timestamp: Date;
  dismissed: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// Real-time data hook options
export interface UseRealTimeDataOptions {
  enabled: boolean;
  updateInterval: number;
  onUpdate?: (data: SystemMetrics) => void;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

// Real-time data hook return
export interface UseRealTimeDataReturn {
  data: SystemMetrics | null;
  loading: boolean;
  error: Error | null;
  lastUpdated: Date | null;
  isConnected: boolean;
  refresh: () => void;
  retry: () => void;
  start: () => void;
  stop: () => void;
}

// Dashboard widget base props
export interface DashboardWidgetProps {
  title: string;
  loading?: boolean;
  error?: string;
  className?: string;
  children: React.ReactNode;
}

// Metric card props
export interface MetricCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  color?: StatusColor;
  loading?: boolean;
  error?: string;
  description?: string;
  trend?: {
    value: number;
    direction: "up" | "down";
    isGood: boolean;
  };
  onClick?: () => void;
  className?: string;
}

// Progress bar props
export interface ProgressBarProps {
  value: number;
  max?: number;
  color?: StatusColor;
  size?: "sm" | "md" | "lg";
  showValue?: boolean;
  animated?: boolean;
  className?: string;
  ariaLabel?: string;
}

// Connection status props
export interface ConnectionStatusProps {
  isConnected: boolean;
  isLive: boolean;
  lastUpdated?: Date;
  className?: string;
}

// Dashboard layout props
export interface DashboardLayoutProps {
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

// Dashboard section props
export interface DashboardSectionProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

// Error boundary state
export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

// Dashboard notification
export interface DashboardNotification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: "primary" | "secondary";
  }>;
}

// Dashboard filter
export interface DashboardFilter {
  timeRange: "1h" | "6h" | "24h" | "7d" | "30d" | "custom";
  customStart?: Date;
  customEnd?: Date;
  metrics: string[];
  refreshInterval: number;
}

// Dashboard export options
export interface DashboardExportOptions {
  format: "pdf" | "csv" | "json";
  timeRange: DashboardFilter["timeRange"];
  metrics: string[];
  includeCharts: boolean;
}

// Dashboard theme
export interface DashboardTheme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: string;
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}
