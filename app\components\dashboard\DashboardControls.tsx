"use client";

import React from "react";
import { DashboardControlsProps } from "@/app/types/dashboard";

export default function DashboardControls({
  isLive,
  isConnected,
  loading,
  error,
  lastUpdated,
  onToggleLive,
  onRefresh,
  onRetry,
  className = "",
}: DashboardControlsProps) {
  return (
    <div className={`flex items-center justify-between bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      {/* Status Section */}
      <div className="flex items-center space-x-4">
        <ConnectionStatus isConnected={isConnected} isLive={isLive} />
        
        {lastUpdated && (
          <LastUpdatedTime lastUpdated={lastUpdated} />
        )}
      </div>

      {/* Controls Section */}
      <div className="flex items-center space-x-2">
        <LiveToggleButton
          isLive={isLive}
          onClick={onToggleLive}
          disabled={loading}
        />

        <RefreshButton
          onClick={onRefresh}
          loading={loading}
          disabled={loading}
        />

        {error && (
          <RetryButton
            onClick={onRetry}
            disabled={loading}
          />
        )}
      </div>
    </div>
  );
}

// Connection status indicator
interface ConnectionStatusProps {
  isConnected: boolean;
  isLive: boolean;
}

function ConnectionStatus({ isConnected, isLive }: ConnectionStatusProps) {
  const getStatusColor = () => {
    if (isConnected && isLive) return "bg-green-500 animate-pulse";
    if (isConnected && !isLive) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getStatusText = () => {
    if (isConnected && isLive) return "Live";
    if (isConnected && !isLive) return "Paused";
    return "Disconnected";
  };

  const getAriaLabel = () => {
    if (isConnected && isLive) return "Connected and live";
    if (isConnected && !isLive) return "Connected but paused";
    return "Disconnected";
  };

  return (
    <div className="flex items-center space-x-2">
      <div
        className={`w-3 h-3 rounded-full ${getStatusColor()}`}
        aria-label={getAriaLabel()}
      />
      <span className="text-sm font-medium text-gray-700">
        {getStatusText()}
      </span>
    </div>
  );
}

// Last updated time display
interface LastUpdatedTimeProps {
  lastUpdated: Date;
}

function LastUpdatedTime({ lastUpdated }: LastUpdatedTimeProps) {
  return (
    <span className="text-xs text-gray-500">
      Last updated: {lastUpdated.toLocaleTimeString()}
    </span>
  );
}

// Live toggle button
interface LiveToggleButtonProps {
  isLive: boolean;
  onClick: () => void;
  disabled?: boolean;
}

function LiveToggleButton({ isLive, onClick, disabled = false }: LiveToggleButtonProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={`px-3 py-1 rounded-md text-sm font-medium transition-colors disabled:opacity-50 ${
        isLive
          ? "bg-red-100 text-red-700 hover:bg-red-200"
          : "bg-green-100 text-green-700 hover:bg-green-200"
      }`}
      aria-label={isLive ? "Pause live updates" : "Resume live updates"}
    >
      {isLive ? (
        <div className="flex items-center space-x-1">
          <PauseIcon />
          <span>Pause</span>
        </div>
      ) : (
        <div className="flex items-center space-x-1">
          <PlayIcon />
          <span>Resume</span>
        </div>
      )}
    </button>
  );
}

// Refresh button
interface RefreshButtonProps {
  onClick: () => void;
  loading?: boolean;
  disabled?: boolean;
}

function RefreshButton({ onClick, loading = false, disabled = false }: RefreshButtonProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm font-medium hover:bg-blue-200 disabled:opacity-50 transition-colors"
      aria-label="Refresh data manually"
    >
      <div className="flex items-center space-x-1">
        <RefreshIcon spinning={loading} />
        <span>{loading ? "Refreshing..." : "Refresh"}</span>
      </div>
    </button>
  );
}

// Retry button
interface RetryButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

function RetryButton({ onClick, disabled = false }: RetryButtonProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md text-sm font-medium hover:bg-yellow-200 disabled:opacity-50 transition-colors"
      aria-label="Retry failed connection"
    >
      <div className="flex items-center space-x-1">
        <RetryIcon />
        <span>Retry</span>
      </div>
    </button>
  );
}

// Icons
function PauseIcon() {
  return (
    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
      <path
        fillRule="evenodd"
        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
        clipRule="evenodd"
      />
    </svg>
  );
}

function PlayIcon() {
  return (
    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
        clipRule="evenodd"
      />
    </svg>
  );
}

function RefreshIcon({ spinning = false }: { spinning?: boolean }) {
  return (
    <svg
      className={`w-4 h-4 ${spinning ? "animate-spin" : ""}`}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
      />
    </svg>
  );
}

function RetryIcon() {
  return (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
      />
    </svg>
  );
}

// Compact controls for smaller spaces
export function CompactDashboardControls({
  isLive,
  isConnected,
  loading,
  onToggleLive,
  onRefresh,
  className = "",
}: Omit<DashboardControlsProps, "error" | "lastUpdated" | "onRetry">) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div
        className={`w-2 h-2 rounded-full ${
          isConnected && isLive
            ? "bg-green-500 animate-pulse"
            : isConnected
            ? "bg-yellow-500"
            : "bg-red-500"
        }`}
        aria-label={
          isConnected && isLive
            ? "Live"
            : isConnected
            ? "Paused"
            : "Disconnected"
        }
      />
      
      <button
        type="button"
        onClick={onToggleLive}
        disabled={loading}
        className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
        aria-label={isLive ? "Pause" : "Resume"}
      >
        {isLive ? <PauseIcon /> : <PlayIcon />}
      </button>
      
      <button
        type="button"
        onClick={onRefresh}
        disabled={loading}
        className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
        aria-label="Refresh"
      >
        <RefreshIcon spinning={loading} />
      </button>
    </div>
  );
}

// Controls skeleton
export function DashboardControlsSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`flex items-center justify-between bg-white rounded-lg shadow-sm border p-4 animate-pulse ${className}`}>
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
          <div className="h-4 bg-gray-300 rounded w-12"></div>
        </div>
        <div className="h-3 bg-gray-300 rounded w-32"></div>
      </div>
      <div className="flex items-center space-x-2">
        <div className="h-8 bg-gray-300 rounded w-16"></div>
        <div className="h-8 bg-gray-300 rounded w-20"></div>
      </div>
    </div>
  );
}
