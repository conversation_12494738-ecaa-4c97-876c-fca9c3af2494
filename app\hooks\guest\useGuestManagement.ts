import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/app/hooks/useNotifications";
import { 
  Guest, 
  GuestFilterType, 
  GuestManagementActions 
} from "@/lib/types/guest-types";
import { filterGuests, getTodayDate } from "@/lib/utils/guest-utils";

interface UseGuestManagementProps {
  initialDate?: string;
  initialFilter?: GuestFilterType;
}

export function useGuestManagement({
  initialDate = getTodayDate(),
  initialFilter = "all"
}: UseGuestManagementProps = {}): GuestManagementActions {
  const toast = useToast();
  const [guests, setGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingGuests, setProcessingGuests] = useState<Set<string>>(new Set());
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [filter, setFilter] = useState<GuestFilterType>(initialFilter);

  // Fetch guests for selected date
  const fetchGuests = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);

      const response = await fetch(
        `/api/reception/guests?date=${selectedDate}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch guests: ${response.status}`);
      }

      const guestsData = await response.json();
      setGuests(guestsData);
    } catch (err) {
      console.error("Error fetching guests:", err);
      setError(err instanceof Error ? err.message : "Failed to load guests");
    } finally {
      setLoading(false);
    }
  }, [selectedDate]);

  // Handle check-in
  const handleCheckIn = useCallback(async (guestId: string) => {
    if (processingGuests.has(guestId)) return;

    setProcessingGuests((prev) => new Set(prev).add(guestId));

    try {
      const response = await fetch(`/api/reception/checkin/${guestId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "CHECKED_IN",
          checkInTime: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to check in guest");
      }

      const updatedGuest = await response.json();
      setGuests((prev) =>
        prev.map((guest) =>
          guest.id === guestId ? { ...guest, status: "CHECKED_IN" } : guest
        )
      );

      toast.success(
        "Check-in Successful",
        `Guest ${updatedGuest.userEmail} has been checked in.`
      );
    } catch (err) {
      console.error("Error checking in guest:", err);
      toast.error(
        "Check-in Failed",
        err instanceof Error ? err.message : "Failed to check in guest"
      );
    } finally {
      setProcessingGuests((prev) => {
        const newSet = new Set(prev);
        newSet.delete(guestId);
        return newSet;
      });
    }
  }, [processingGuests, toast]);

  // Handle check-out
  const handleCheckOut = useCallback(async (guestId: string) => {
    if (processingGuests.has(guestId)) return;

    setProcessingGuests((prev) => new Set(prev).add(guestId));

    try {
      const response = await fetch(`/api/reception/checkout/${guestId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "CHECKED_OUT",
          checkOutTime: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to check out guest");
      }

      const updatedGuest = await response.json();
      setGuests((prev) =>
        prev.map((guest) =>
          guest.id === guestId ? { ...guest, status: "CHECKED_OUT" } : guest
        )
      );

      toast.success(
        "Check-out Successful",
        `Guest ${updatedGuest.userEmail} has been checked out.`
      );
    } catch (err) {
      console.error("Error checking out guest:", err);
      toast.error(
        "Check-out Failed",
        err instanceof Error ? err.message : "Failed to check out guest"
      );
    } finally {
      setProcessingGuests((prev) => {
        const newSet = new Set(prev);
        newSet.delete(guestId);
        return newSet;
      });
    }
  }, [processingGuests, toast]);

  // Get filtered guests
  const filteredGuests = filterGuests(guests, filter);

  // Fetch guests when date changes
  useEffect(() => {
    fetchGuests();
  }, [fetchGuests]);

  return {
    guests,
    loading,
    error,
    processingGuests,
    selectedDate,
    filter,
    fetchGuests,
    handleCheckIn,
    handleCheckOut,
    setSelectedDate,
    setFilter,
    filteredGuests,
  };
}
