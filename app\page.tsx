import { Suspense } from "react";
import Hero from "./components/Hero";
import ResortsPreview from "./components/ResortsPreview";
import SpaSection from "./components/SpaSection";
import Gallery from "./components/Gallery";
import DynamicTestimonials from "./components/DynamicTestimonials";
import Newsletter from "./components/Newsletter";
import { CardSkeleton, GridSkeleton } from "./components/ui/SkeletonComponents";
import ErrorBoundary from "./components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface HomePageProps {
  searchParams?: {
    section?: string;
  };
}

// Loading components for different sections
function TestimonialsLoading() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
        </div>
        <GridSkeleton count={6} columns={3} SkeletonComponent={CardSkeleton} />
      </div>
    </section>
  );
}

export default function Home({ searchParams }: HomePageProps) {
  return (
    <main role="main" aria-label="Kuriftu Water Park homepage">
      {/* Hero Section */}
      <ErrorBoundary>
        <Hero />
      </ErrorBoundary>

      {/* Resorts Preview Section */}
      <ErrorBoundary>
        <ResortsPreview />
      </ErrorBoundary>

      {/* Spa Section */}
      <ErrorBoundary>
        <SpaSection />
      </ErrorBoundary>

      {/* Gallery Section */}
      <ErrorBoundary>
        <Gallery />
      </ErrorBoundary>

      {/* Testimonials Section with Loading State */}
      <ErrorBoundary>
        <Suspense fallback={<TestimonialsLoading />}>
          <DynamicTestimonials maxItems={6} showTitle={true} />
        </Suspense>
      </ErrorBoundary>

      {/* Newsletter Section */}
      <ErrorBoundary>
        <Newsletter />
      </ErrorBoundary>
    </main>
  );
}
