import { prisma } from "./prisma";
import { logAuditEvent } from "./security";
import type { NextRequest, NextResponse } from "next/server";

interface BookingWithRelations {
  id: string;
  userEmail: string;
  resortId: string | null;
  roomId: string | null;
  spaId: string | null;
  experienceId: string | null;
  wellnessId: string | null;
  eventId: string | null;
  checkIn: Date;
  checkOut: Date | null;
  status: string;
  totalAmount: number | null;
  createdAt: Date;
  resort?: { id: string; name: string } | null;
  spaTreatment?: { id: string; name: string } | null;
}

interface MetricData {
  calls: number;
  totalDuration: number;
  errors: number;
}

interface UserActionMetadata {
  [key: string]: string | number | boolean | null;
}

export interface BookingAnalytics {
  totalBookings: number;
  bookingsByStatus: Record<string, number>;
  bookingsByType: Record<string, number>;
  revenueByMonth: Array<{ month: string; revenue: number; bookings: number }>;
  revenueByQuarter: Array<{ quarter: string; revenue: number; growth: number }>;
  popularResorts: Array<{ name: string; bookings: number; revenue: number }>;
  popularSpaTreatments: Array<{
    name: string;
    bookings: number;
    revenue: number;
  }>;
  averageBookingValue: number;
  cancellationRate: number;
  occupancyRate: number;
  seasonalTrends: Array<{ period: string; bookings: number; revenue: number }>;
  customerRetentionRate: number;
  averageLeadTime: number;
  peakBookingHours: Array<{ hour: number; bookings: number }>;
  weekdayVsWeekendRatio: { weekday: number; weekend: number };
}

export interface SystemMetrics {
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseConnections: number;
  memoryUsage: number;
  cpuUsage: number;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private metrics: Map<string, MetricData> = new Map();

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Booking Analytics
  async getBookingAnalytics(
    startDate: Date = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
    endDate: Date = new Date()
  ): Promise<BookingAnalytics> {
    try {
      // Get all bookings in date range
      const bookings = await prisma.booking.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          resort: true,
          spaTreatment: true,
        },
      });

      // Calculate metrics
      const totalBookings = bookings.length;

      const bookingsByStatus = bookings.reduce((acc, booking) => {
        acc[booking.status] = (acc[booking.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const bookingsByType = bookings.reduce((acc, booking) => {
        const type = booking.resortId ? "Resort" : "Spa";
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Revenue by month (simplified - would need pricing data)
      const revenueByMonth = await this.calculateRevenueByMonth(
        startDate,
        endDate
      );

      // Get booking data for popular items calculation
      const resortBookings = bookings.filter((b) => b.resort);
      const spaBookings = bookings.filter((b) => b.spaTreatment);

      // Calculate rates
      const cancelledBookings = bookings.filter(
        (b) => b.status === "CANCELLED"
      ).length;
      const cancellationRate =
        totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;

      // Simplified occupancy rate calculation
      const confirmedBookings = bookings.filter(
        (b) => b.status === "CONFIRMED"
      ).length;
      const occupancyRate =
        totalBookings > 0 ? (confirmedBookings / totalBookings) * 100 : 0;

      // Enhanced analytics
      const revenueByQuarter = await this.calculateRevenueByQuarter(
        startDate,
        endDate
      );
      const seasonalTrends = await this.calculateSeasonalTrends(
        startDate,
        endDate
      );
      const customerRetentionRate = await this.calculateCustomerRetentionRate();
      const averageLeadTime = this.calculateAverageLeadTime(bookings);
      const peakBookingHours = this.calculatePeakBookingHours(bookings);
      const weekdayVsWeekendRatio = this.calculateWeekdayWeekendRatio(bookings);

      // Enhanced popular items with revenue
      const popularResortsWithRevenue = this.getPopularItemsWithRevenue(
        resortBookings,
        "resort"
      );
      const popularSpaTreatmentsWithRevenue = this.getPopularItemsWithRevenue(
        spaBookings,
        "spaTreatment"
      );

      return {
        totalBookings,
        bookingsByStatus,
        bookingsByType,
        revenueByMonth,
        revenueByQuarter,
        popularResorts: popularResortsWithRevenue,
        popularSpaTreatments: popularSpaTreatmentsWithRevenue,
        averageBookingValue: this.calculateAverageBookingValue(bookings),
        cancellationRate,
        occupancyRate,
        seasonalTrends,
        customerRetentionRate,
        averageLeadTime,
        peakBookingHours,
        weekdayVsWeekendRatio,
      };
    } catch (error) {
      console.error("Error calculating booking analytics:", error);
      throw error;
    }
  }

  private async calculateRevenueByMonth(startDate: Date, endDate: Date) {
    // Enhanced revenue calculation with booking count
    const months = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const monthStart = new Date(current.getFullYear(), current.getMonth(), 1);
      const monthEnd = new Date(
        current.getFullYear(),
        current.getMonth() + 1,
        0
      );

      const monthBookings = await prisma.booking.count({
        where: {
          createdAt: {
            gte: monthStart,
            lte: monthEnd,
          },
          status: "CONFIRMED",
        },
      });

      months.push({
        month: current.toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
        }),
        revenue: monthBookings * 200, // Simplified calculation
        bookings: monthBookings,
      });

      current.setMonth(current.getMonth() + 1);
    }

    return months;
  }

  private async calculateRevenueByQuarter(startDate: Date, endDate: Date) {
    const quarters = [];
    const currentYear = startDate.getFullYear();
    const endYear = endDate.getFullYear();

    for (let year = currentYear; year <= endYear; year++) {
      for (let quarter = 1; quarter <= 4; quarter++) {
        const quarterStart = new Date(year, (quarter - 1) * 3, 1);
        const quarterEnd = new Date(year, quarter * 3, 0);

        if (quarterStart > endDate) break;
        if (quarterEnd < startDate) continue;

        const quarterBookings = await prisma.booking.count({
          where: {
            createdAt: {
              gte: quarterStart,
              lte: quarterEnd,
            },
            status: "CONFIRMED",
          },
        });

        const revenue = quarterBookings * 200;
        const previousQuarterRevenue: number =
          quarters.length > 0 ? quarters[quarters.length - 1].revenue : revenue;
        const growth =
          previousQuarterRevenue > 0
            ? ((revenue - previousQuarterRevenue) / previousQuarterRevenue) *
              100
            : 0;

        quarters.push({
          quarter: `Q${quarter} ${year}`,
          revenue,
          growth: Math.round(growth * 100) / 100,
        });
      }
    }

    return quarters;
  }

  private async calculateSeasonalTrends(startDate: Date, endDate: Date) {
    const seasons = [
      { name: "Spring", months: [2, 3, 4] },
      { name: "Summer", months: [5, 6, 7] },
      { name: "Fall", months: [8, 9, 10] },
      { name: "Winter", months: [11, 0, 1] },
    ];

    const trends = [];

    for (const season of seasons) {
      let totalBookings = 0;
      let totalRevenue = 0;

      for (const _ of season.months) {
        const monthBookings = await prisma.booking.count({
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
            status: "CONFIRMED",
          },
        });

        totalBookings += monthBookings;
        totalRevenue += monthBookings * 200;
      }

      trends.push({
        period: season.name,
        bookings: totalBookings,
        revenue: totalRevenue,
      });
    }

    return trends;
  }

  async trackApiCall(
    endpoint: string,
    method: string,
    duration: number,
    success: boolean
  ): Promise<void> {
    const key = `api_${endpoint}_${method}`;
    const existing = this.metrics.get(key) || {
      calls: 0,
      totalDuration: 0,
      errors: 0,
    };

    existing.calls += 1;
    existing.totalDuration += duration;
    if (!success) existing.errors += 1;

    this.metrics.set(key, existing);

    // Log slow API calls
    if (duration > 2000) {
      await logAuditEvent({
        action: "SLOW_API_CALL",
        resource: endpoint,
        ip: "system",
        userAgent: "performance_monitor",
        success: true,
        error: `${method} ${endpoint} took ${duration}ms`,
      });
    }
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    const apiMetrics = Array.from(this.metrics.entries())
      .filter(([key]) => key.startsWith("api_"))
      .map(([, value]) => value);

    const totalCalls = apiMetrics.reduce(
      (sum, metric) => sum + metric.calls,
      0
    );
    const totalDuration = apiMetrics.reduce(
      (sum, metric) => sum + metric.totalDuration,
      0
    );
    const totalErrors = apiMetrics.reduce(
      (sum, metric) => sum + metric.errors,
      0
    );

    const averageResponseTime = totalCalls > 0 ? totalDuration / totalCalls : 0;
    const errorRate = totalCalls > 0 ? (totalErrors / totalCalls) * 100 : 0;

    // Get active users (simplified - users with activity in last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const activeUsers = await prisma.booking.findMany({
      where: {
        createdAt: {
          gte: oneHourAgo,
        },
      },
      select: {
        userEmail: true,
      },
      distinct: ["userEmail"],
    });

    return {
      responseTime: Math.round(averageResponseTime),
      errorRate: Math.round(errorRate * 100) / 100,
      activeUsers: activeUsers.length,
      databaseConnections: 0, // Would need database-specific monitoring
      memoryUsage: 0, // Would need system monitoring
      cpuUsage: 0, // Would need system monitoring
    };
  }

  // User Behavior Analytics
  async trackUserAction(
    userId: string,
    action: string,
    metadata?: UserActionMetadata
  ) {
    try {
      await logAuditEvent({
        userId,
        action: `USER_ACTION_${action.toUpperCase()}`,
        resource: "user_behavior",
        ip: "system",
        userAgent: "analytics_service",
        success: true,
        error: metadata ? JSON.stringify(metadata) : undefined,
      });
    } catch (error) {
      console.error("Error tracking user action:", error);
    }
  }

  async getSecurityMetrics() {
    return {
      failedLogins: 0,
      suspiciousActivity: 0,
      blockedRequests: 0,
      securityAlerts: [],
      topThreats: [],
    };
  }

  // Booking Patterns
  async getBookingPatterns() {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const bookings = await prisma.booking.findMany({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
      select: {
        createdAt: true,
        checkIn: true,
        status: true,
        resortId: true,
        spaId: true,
      },
    });

    // Analyze booking patterns by day of week
    const dayOfWeekPattern = bookings.reduce((acc, booking) => {
      const day = booking.createdAt.getDay();
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    // Analyze booking patterns by hour
    const hourPattern = bookings.reduce((acc, booking) => {
      const hour = booking.createdAt.getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    // Lead time analysis (days between booking and check-in)
    const leadTimes = bookings
      .filter((b) => b.checkIn)
      .map((b) => {
        const leadTime = Math.floor(
          (b.checkIn.getTime() - b.createdAt.getTime()) / (1000 * 60 * 60 * 24)
        );
        return leadTime;
      });

    const averageLeadTime =
      leadTimes.length > 0
        ? leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length
        : 0;

    return {
      dayOfWeekPattern,
      hourPattern,
      averageLeadTime: Math.round(averageLeadTime),
      totalBookings: bookings.length,
    };
  }

  // Additional helper methods for enhanced analytics
  private getPopularItemsWithRevenue(
    bookings: BookingWithRelations[],
    type: "resort" | "spaTreatment"
  ) {
    const items = bookings.reduce((acc, booking) => {
      const item = booking[type];
      if (item) {
        const name = item.name;
        if (!acc[name]) {
          acc[name] = { bookings: 0, revenue: 0 };
        }
        acc[name].bookings += 1;
        acc[name].revenue += 200; // Simplified revenue calculation
      }
      return acc;
    }, {} as Record<string, { bookings: number; revenue: number }>);

    return Object.entries(items)
      .map(([name, data]) => ({
        name,
        bookings: data.bookings,
        revenue: data.revenue,
      }))
      .sort((a, b) => b.bookings - a.bookings)
      .slice(0, 5);
  }

  private calculateAverageBookingValue(
    bookings: BookingWithRelations[]
  ): number {
    // Simplified calculation - would need actual pricing data
    const confirmedBookings = bookings.filter((b) => b.status === "CONFIRMED");
    return confirmedBookings.length > 0 ? 200 : 0; // Placeholder value
  }

  private async calculateCustomerRetentionRate(): Promise<number> {
    // Calculate customers who made multiple bookings
    const userBookings = await prisma.booking.groupBy({
      by: ["userEmail"],
      _count: {
        id: true,
      },
      having: {
        id: {
          _count: {
            gt: 1,
          },
        },
      },
    });

    const totalUsers = await prisma.booking.findMany({
      select: { userEmail: true },
      distinct: ["userEmail"],
    });

    return totalUsers.length > 0
      ? (userBookings.length / totalUsers.length) * 100
      : 0;
  }

  private calculateAverageLeadTime(bookings: BookingWithRelations[]): number {
    const leadTimes = bookings
      .filter((b) => b.checkIn)
      .map((b) => {
        const leadTime = Math.floor(
          (new Date(b.checkIn).getTime() - new Date(b.createdAt).getTime()) /
            (1000 * 60 * 60 * 24)
        );
        return leadTime;
      });

    return leadTimes.length > 0
      ? Math.round(
          leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length
        )
      : 0;
  }

  private calculatePeakBookingHours(
    bookings: BookingWithRelations[]
  ): Array<{ hour: number; bookings: number }> {
    const hourCounts = bookings.reduce((acc, booking) => {
      const hour = new Date(booking.createdAt).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return Object.entries(hourCounts)
      .map(([hour, bookings]) => ({
        hour: parseInt(hour),
        bookings: bookings as number,
      }))
      .sort((a, b) => b.bookings - a.bookings)
      .slice(0, 5);
  }

  private calculateWeekdayWeekendRatio(bookings: BookingWithRelations[]): {
    weekday: number;
    weekend: number;
  } {
    const weekdayBookings = bookings.filter((b) => {
      const day = new Date(b.createdAt).getDay();
      return day >= 1 && day <= 5; // Monday to Friday
    }).length;

    const weekendBookings = bookings.filter((b) => {
      const day = new Date(b.createdAt).getDay();
      return day === 0 || day === 6; // Saturday and Sunday
    }).length;

    const total = weekdayBookings + weekendBookings;
    return {
      weekday: total > 0 ? Math.round((weekdayBookings / total) * 100) : 0,
      weekend: total > 0 ? Math.round((weekendBookings / total) * 100) : 0,
    };
  }

  // Clear metrics (for testing or periodic cleanup)
  clearMetrics() {
    this.metrics.clear();
  }

  // Export analytics data
  async exportAnalytics(format: "json" | "csv" = "json") {
    const analytics = await this.getBookingAnalytics();
    const systemMetrics = await this.getSystemMetrics();
    const patterns = await this.getBookingPatterns();

    const data = {
      timestamp: new Date().toISOString(),
      bookingAnalytics: analytics,
      systemMetrics,
      bookingPatterns: patterns,
    };

    if (format === "json") {
      return JSON.stringify(data, null, 2);
    }

    // CSV export would require additional formatting
    return data;
  }
}

// Singleton instance
export const analytics = AnalyticsService.getInstance();

// Middleware for automatic API tracking
export function createAnalyticsMiddleware() {
  return async (
    req: NextRequest,
    res: NextResponse,
    next: () => Promise<void>
  ): Promise<void> => {
    const start = Date.now();
    const endpoint = new URL(req.url).pathname;
    const method = req.method;

    try {
      await next();
      const duration = Date.now() - start;
      await analytics.trackApiCall(endpoint, method, duration, true);
    } catch (error) {
      const duration = Date.now() - start;
      await analytics.trackApiCall(endpoint, method, duration, false);
      throw error;
    }
  };
}
